import { apiService } from "../api";
const ENDPOINT = "v1/admin/stream-post";

export const streamPostService = apiService.injectEndpoints({
  endpoints: (build) => ({
    listStreamPost: build.query<TReponsePaging<TStream>, TQueryAPI>({
      query: (params) => {
        return {
          url: ENDPOINT,
          method: "GET",
          params,
        };
      },
      transformResponse: (rawResult: TReponsePaging<TStream>) => {
        return rawResult;
      },
    }),
    getStreamPostById: build.query<TStream, string>({
      query: (id: string) => {
        return {
          url: ENDPOINT + "/" + id,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TStream) => {
        return rawResult;
      },
    }),
    createStreamPost: build.mutation({
      query: (payload) => {
        return {
          url: ENDPOINT,
          method: "POST",
          data: payload,
        };
      },
      transformResponse: (rawResult: TStream) => {
        return rawResult;
      },
    }),
    updateStreamPost: build.mutation({
      query: ({ id, ...payload }) => {
        return {
          url: ENDPOINT + "/" + id,
          method: "PUT",
          data: payload,
        };
      },
      transformResponse: (rawResult: TStream) => {
        return rawResult;
      },
    }),
    deleteStreamPostById: build.query<TStream, string>({
      query: (id: string) => {
        return {
          url: ENDPOINT + "/" + id,
          method: "DELETE",
        };
      },
      transformResponse: (rawResult: TStream) => {
        return rawResult;
      },
    }),
    sendStreamPostMetadata: build.mutation({
      query: ({ streamId, ...payload }) => {
        return {
          url: ENDPOINT + `/${streamId}/metadata`,
          method: "POST",
          data: payload
        };
      },
    }),
    // getStreamPostViewerCount: build.query({
    //   query: ({ streamId }) => {
    //     return {
    //       url: ENDPOINT + `/${streamId}/viewer-count`,
    //       method: "GET",
    //     };
    //   },
    // }),
    generateStreamPostKey: build.mutation({
      query: ({ streamId, ...payload }) => {
        return {
          url: ENDPOINT + `/${streamId}/stream-key`,
          method: "POST",
          data: payload
        };
      },
    }),
    startBroadcastStreamPost: build.mutation({
      query: ({ streamId, ...payload }) => {
        return {
          url: ENDPOINT + `/${streamId}/start-broadcast`,
          method: "POST",
          data: payload
        };
      },
    }),
    sendNotificationStreamPost: build.mutation({
      query: ({ streamId }) => {
        return {
          url: ENDPOINT + `/${streamId}/notifications`,
          method: "POST",
        };
      },
    }),
    endBroadcastStreamPost: build.mutation({
      query: ({ streamId, ...payload }) => {
        return {
          url: ENDPOINT + `/${streamId}/end-broadcast`,
          method: "POST",
          data: payload
        };
      },
    }),
    getStreamPostStatistics: build.query({
      query: ({ streamId, ...params }) => {
        return {
          url: ENDPOINT + `/${streamId}/statistics`,
          method: "GET",
          params
        };
      },
    }),
    getStreamViewersFullList: build.query({
      query: ({ streamId, ...params }) => {
        return {
          url: ENDPOINT + `/${streamId}/viewers-full`,
          method: "GET",
          params
        };
      },
    }),
    getStreamViewersPaginatedList: build.query({
      query: ({ streamId, ...params }) => {
        return {
          url: ENDPOINT + `/${streamId}/viewers-paginated`,
          method: "GET",
          params
        };
      },
    }),
    getStreamUserInteractionsList: build.query({
      query: ({ streamId, ...params }) => {
        return {
          url: ENDPOINT + `/${streamId}/user-interactions`,
          method: "GET",
          params
        };
      },
    }),
    saveStreamRafflePrize: build.mutation({
      query: ({ streamId, ...params }) => {
        return {
          url: ENDPOINT + `/${streamId}/raffle-winner`,
          method: "POST",
          params
        };
      },
    }),
    removeStreamRaffleParticipant: build.mutation({
      query: ({ streamId, ...params }) => {
        return {
          url: ENDPOINT + `/${streamId}/remove-participant`,
          method: "POST",
          params
        };
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyListStreamPostQuery,
  useLazyGetStreamPostByIdQuery,
  useLazyDeleteStreamPostByIdQuery,
  useGenerateStreamPostKeyMutation,
  useCreateStreamPostMutation,
  useUpdateStreamPostMutation,
  useSendStreamPostMetadataMutation,
  useStartBroadcastStreamPostMutation,
  useEndBroadcastStreamPostMutation,
  useLazyGetStreamPostStatisticsQuery,
  // useLazyGetStreamPostViewerCountQuery,
  useSendNotificationStreamPostMutation,
  useLazyGetStreamViewersFullListQuery,
  useLazyGetStreamViewersPaginatedListQuery,
  useLazyGetStreamUserInteractionsListQuery,
  useSaveStreamRafflePrizeMutation,
  useRemoveStreamRaffleParticipantMutation,
} = streamPostService;
