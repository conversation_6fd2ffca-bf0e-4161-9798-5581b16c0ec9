import { apiService } from "./api";
const ENDPOINT = "v1/admin/product";

export const productService = apiService.injectEndpoints({
  endpoints: (build) => ({
    listProduct: build.query<TReponsePaging<TProduct>, TQueryAPI>({
      query: (params) => {
        return {
          url: ENDPOINT,
          method: "GET",
          params,
        };
      },
      transformResponse: (rawResult: TReponsePaging<TProduct>) => {
        const data = rawResult.data?.map((item: TProduct, index) => ({
          ...item,
          _rowIndex: index + 1,
        }));
        return { ...rawResult, data: data || [] };
      },
    }),
    getProductById: build.query<TProduct, string>({
      query: (id: string) => {
        return {
          url: ENDPOINT + "/" + id,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TProduct) => {
        return rawResult;
      },
    }),
    selectProduct: build.query({
      query: (params) => {
        return {
          url: ENDPOINT + "/select",
          method: "GET",
          params,
        };
      },
    }),
    createProduct: build.mutation<
      TProduct,
      Partial<TProduct>
    >({
      query: (payload) => {
        return {
          url: ENDPOINT,
          method: "POST",
          data: payload,
        };
      },
      transformResponse: (rawResult: TProduct) => {
        return rawResult;
      },
    }),
    updateProduct: build.mutation<
      TProduct,
      Partial<TProduct>
    >({
      query: ({ id, ...payload }) => {
        return {
          url: ENDPOINT + `/${id}`,
          method: "PUT",
          data: payload,
        };
      },
      transformResponse: (rawResult: TProduct) => {
        return rawResult;
      },
    }),
    syncProduct: build.mutation({
      query: ({ id, ...payload }) => {
        return {
          url: ENDPOINT + `/${id}/sync`,
          method: "PUT",
          data: payload,
        };
      },
    }),
    approveProduct: build.mutation({
      query: (payload) => {
        return {
          url: ENDPOINT + `/approval`,
          method: "PUT",
          data: payload,
        };
      },
    }),
    deleteProductById: build.mutation<
      TProduct,
      string
    >({
      query: (id: string) => {
        return {
          url: ENDPOINT + "/" + id,
          method: "DELETE",
        };
      },
      transformResponse: (rawResult: TProduct) => {
        return rawResult;
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyListProductQuery,
  useLazySelectProductQuery,
  useLazyGetProductByIdQuery,
  useCreateProductMutation,
  useUpdateProductMutation,
  useSyncProductMutation,
  useApproveProductMutation,
  useDeleteProductByIdMutation,
} = productService;
