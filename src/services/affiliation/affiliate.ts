import { TAffiliateCommission } from "../../types/affiliation/affiliate-commission";
import { apiService } from "../api";
const ENDPOINT = "v1/admin/affiliates";

export const affiliateService = apiService.injectEndpoints({
  endpoints: (build) => ({

    getAffiliteLists: build.query<
      TReponsePaging<TAffiliate>,
      TQueryAPI
    >({
      query: (params) => ({
        url: ENDPOINT + "/",
        method: "GET",
        params,
      }),
      transformResponse: (rawResult: TReponsePaging<TAffiliate>) => {
        if (Array(rawResult)) {
          return rawResult;
        } else {
          const data = rawResult.data?.map((item: TAffiliate, index) => ({
            ...item,
            _rowIndex: index + 1,
          }));
          return { ...rawResult, data: data || [] };
        }
      },
    }),

    activateAffiliteForUser: build.mutation<
      TAffiliate,
      {
        userId: string,
      }
    >({
      query: (params) => ({
        url: ENDPOINT + '/',
        method: 'POST',
        params
      })
    }),

    getAffiliateById: build.query<TAffiliate, string>({
      query: (id) => ({
        url: `${ENDPOINT}/${id}`,
        method: "GET",
      }),
    }),

    getReferredCustomersCount: build.query<number, string>({
      query: (affiliateId) => ({
        url: `${ENDPOINT}/${affiliateId}/referred-customers-count`,
        method: "GET",
      }),
    }),

    getCommisisons: build.query<
      TReponsePaging<TAffiliateCommission>,
      {
        affiliateId: string,
        page: number,
        limit: number,
        startDate?: string,
        endDate?: string
      }
    >({
      query: (params) => ({
        url: `${ENDPOINT}/${params.affiliateId}/commissions`,
        method: "GET",
        params: {
          page: params.page,
          limit: params.limit,
          startDate: params.startDate,
          endDate: params.endDate,
        },
      })
    }),

    getCommissionRateSummary: build.query<
      TAffiliateCommissionRateSummary,
      {
        affiliateId: string,
        startDate?: string,
        endDate?: string
      }
    >({
      query: (params) => ({
        url: `${ENDPOINT}/${params.affiliateId}/commission-rate-summary`,
        method: "GET",
        params: params
      }),
    }),

    calculateCommissions: build.mutation<
      {
        success: boolean;
        message: string;
      },
      {
        affiliateId: string,
        startDate?: string,
        endDate?: string
      }
    >({
      query: (payload) => {
        return {
          url: `${ENDPOINT}/${payload.affiliateId}/calculate-commissions`,
          method: "PATCH",
          params: {
            startDate: payload.startDate,
            endDate: payload.endDate
          },
        };
      },
    }),

    getCommissionStats: build.query<
      TAffiliateCommissionStats,
      {
        affiliateId: string,
        startDate: string,
        endDate: string
      }
    >({
      query: (params) => ({
        url: `${ENDPOINT}/${params.affiliateId}/commission-stats`,
        method: "GET",
        params: params
      }),
    }),

    payForCommissionRequest: build.mutation<
      TAffiliateCommisionPayment, {
        affiliateId: string,
        amount: number,
        paymentMethodId: string,
      }
    >({
      query: (payload) => {
        return {
          url: `${ENDPOINT}/${payload.affiliateId}/pay`,
          method: "POST",
          data: payload,
        };
      },
      transformResponse: (rawResult: TAffiliateCommisionPayment) => {
        return rawResult;
      },
    }),

    getPaymentMethods: build.query<TPaymentMethod[], string>({
      query: (affiliateId) => ({
        url: `${ENDPOINT}/${affiliateId}/payment-methods`,
        method: "GET",
      }),
    }),

    verifyRegistrationRequest: build.mutation<
      TAffiliate,
      TAffiliateApprovalRequest
    >({
      query: (payload) => {
        return {
          url: `${ENDPOINT}/${payload.affiliateId}`,
          method: "PUT",
          data: { ...payload, action: 'approval' },
        };
      },
      transformResponse: (rawResult: TAffiliate) => {
        return rawResult;
      },
    }),

    setRefCode: build.mutation<
      TAffiliate,
      {
        affiliateId: string,
        refCode: string
      }
    >({
      query: (payload) => {
        return {
          url: `${ENDPOINT}/${payload.affiliateId}`,
          method: "PUT",
          data: { ...payload, action: 'set-ref-code' },
        };
      },
      transformResponse: (rawResult: TAffiliate) => {
        return rawResult;
      },
    }),

    updateTier: build.mutation<
      TAffiliate,
      {
        affiliateId: string,
        tierAction: string
      }
    >({
      query: (payload) => {
        return {
          url: `${ENDPOINT}/${payload.affiliateId}`,
          method: "PUT",
          data: { ...payload, action: 'update-tier' },
        };
      },
      transformResponse: (rawResult: TAffiliate) => {
        return rawResult;
      },
    }),

    previewSyncData: build.mutation<TAffiliate, string>({
      query: (affiliateId) => {
        return {
          url: `${ENDPOINT}/${affiliateId}/preview-sync-data`,
          method: "PATCH"
        };
      },
      transformResponse: (rawResult: TAffiliate) => {
        return rawResult;
      },
    }),

    proceedSyncData: build.mutation<
      TAffiliate,
      {
        affiliateId: string,
        updatedData: any
      }
    >({
      query: (payload) => {
        return {
          url: `${ENDPOINT}/${payload.affiliateId}/proceed-sync-data`,
          method: "PATCH",
          data: payload.updatedData
        };
      },
      transformResponse: (rawResult: TAffiliate) => {
        return rawResult;
      },
    }),

    deleteAffiliateById: build.mutation<string, string>({
      query: (affiliateId) => ({
        url: `${ENDPOINT}/${affiliateId}`,
        method: "DELETE"
      }),
    }),


  })
});

const campaignService = apiService.injectEndpoints({
  endpoints: (build) => ({
    selectAffiliate: build.query<TReponsePaging<TEvent>, TQueryAPI>({
      query: (params) => {
        return {
          url: ENDPOINT + "/select",
          method: "GET",
          params,
        };
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyGetAffiliteListsQuery,
  useActivateAffiliteForUserMutation,
  useLazyGetAffiliateByIdQuery,
  useLazyGetReferredCustomersCountQuery,
  useLazyGetCommisisonsQuery,
  useLazyGetCommissionRateSummaryQuery,
  useLazyGetCommissionStatsQuery,
  useLazyGetPaymentMethodsQuery,
  useCalculateCommissionsMutation,
  usePayForCommissionRequestMutation,

  useVerifyRegistrationRequestMutation,

  useSetRefCodeMutation,
  useUpdateTierMutation,
  usePreviewSyncDataMutation,
  useProceedSyncDataMutation,
  useDeleteAffiliateByIdMutation
} = affiliateService;

export const { useSelectAffiliateQuery } = campaignService;
