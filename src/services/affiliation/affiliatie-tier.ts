import { EListAction } from "../../components/enums/list-action";
import { apiService } from "../api";
const ENDPOINT = "v1/admin/affiliate-tiers";

export const affiliateTierService = apiService.injectEndpoints({
  endpoints: (builder) => ({

    listAffiliateTier: builder.query<
      TReponsePaging<TAffiliateTier>,
      TQueryAPI
    >({
      query: (params) => ({
        url: ENDPOINT,
        method: "GET",
        params,
      }),
      transformResponse: (rawResult: TReponsePaging<TAffiliateTier>) => {
        if (Array(rawResult)) {
          return rawResult;
        } else {
          const data = rawResult.data?.map((item: TAffiliateTier, index) => ({
            ...item,
            _rowIndex: index + 1,
          }));
          return { ...rawResult, data: data || [] };
        }
      },
    }),

    getAffiliateTierById: builder.query<
      TAffiliateTier,
      {
        id: string,
        loadCommissionCollection?: boolean,
        loadDiscountCollection?: boolean
      }
    >({
      query: ({ id, loadCommissionCollection, loadDiscountCollection }) => ({
        url: `${ENDPOINT}/${id}`,
        method: "GET",
        params: {
          loadCommissionCollection,
          loadDiscountCollection
        }
      }),
    }),

    getNextAffiliateTier: builder.query<TAffiliateTier, string>({
      query: (id) => ({
        url: `${ENDPOINT}/${id}/next`,
        method: "GET",
      }),
    }),

    getDiscountedProducts: builder.query<TReponsePaging<TProduct>, { id, searchTerm, page, limit }>({
      query: ({ id, ...params }) => ({
        url: `${ENDPOINT}/${id}/discounted-products`,
        method: "GET",
        params
      }),
    }),

    getFullPricedProducts: builder.query<TReponsePaging<TProduct>, { id, collectionId, searchTerm, page, limit }>({
      query: ({ id, ...params }) => ({
        url: `${ENDPOINT}/${id}/full-priced-products`,
        method: "GET",
        params
      }),
    }),

    createAffiliateTier: builder.mutation<
      TAffiliateTier,
      Partial<TAffiliateTier>
    >({
      query: (payload) => ({
        url: ENDPOINT,
        method: "POST",
        data: payload,
      }),
    }),

    updateAffiliateTier: builder.mutation<
      TAffiliateTier,
      Partial<TAffiliateTier> & { id: string }
    >({
      query: ({ id, ...payload }) => ({
        url: `${ENDPOINT}/${id}`,
        method: "PUT",
        data: payload,
      }),
    }),

    updateDiscountCollectionByCollection: builder.mutation<
      TAffiliateTier,
      {
        id: string,
        collectionId: string
      }
    >({
      query: ({ id, collectionId }) => ({
        url: `${ENDPOINT}/${id}/discount-collection-by-collection`,
        method: "PATCH",
        data: { collectionId }
      })
    }),

    updateDiscountCollectionByProductIds: builder.mutation<
      TAffiliateTier,
      {
        id: string,
        action: EListAction,
        productIds: string[],
        collectionId?: string
      }
    >({
      query: ({ id, action, productIds, collectionId }) => ({
        url: `${ENDPOINT}/${id}/discount-collection-by-products`,
        method: "PATCH",
        data: {
          action,
          productIds,
          collectionId
        }
      })
    }),

    deleteAffiliateTierById: builder.mutation<TAffiliateTier, string>({
      query: (id) => ({
        url: `${ENDPOINT}/${id}`,
        method: "DELETE",
      }),
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyListAffiliateTierQuery,
  useLazyGetAffiliateTierByIdQuery,
  useLazyGetNextAffiliateTierQuery,

  useLazyGetDiscountedProductsQuery,
  useLazyGetFullPricedProductsQuery,
  useCreateAffiliateTierMutation,
  useUpdateAffiliateTierMutation,
  useUpdateDiscountCollectionByCollectionMutation,
  useUpdateDiscountCollectionByProductIdsMutation,
  useDeleteAffiliateTierByIdMutation,
} = affiliateTierService;
