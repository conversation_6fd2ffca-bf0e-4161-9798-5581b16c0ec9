import {apiService} from '../api'
import {TChatResource, TChatSuggestion, TChatTokenResponse} from "../../types/chat.ts";


const ENDPOINT = "v1/admin/chat-bot"

export const chatBotService = apiService.injectEndpoints({
    endpoints: (build) => ({

        getChatRoom: build.query<TChatBotRoom, void>({
            query: () => ({
                url: ENDPOINT,
                method: 'GET',
            }),
            transformResponse: (raw: TChatBotRoom) => ({
                ...raw,
            }),
        }),

        listChatRooms : build.query<TReponsePaging<TChatBotRoom>, {page?: number; limit?: number}>({
            query: ({page =1, limit=20}) => ({
                url: `${ENDPOINT}/rooms`,
                method: "GET",
                params: {page, limit}
            }),
            transformResponse: (raw: TReponsePaging<TChatBotRoom>) => ({
                ...raw,
                data: (raw?.data ?? []).map((room,index) => ({ ...room, _rowIndex: index +1}))
            })
        }),

        listChatResources: build.query<TChatResource[], void>({
            query: () => ({
                url: `${ENDPOINT}/resources`,
                method: "GET"
            }),
            transformResponse: (raw: TChatResource[]) => raw,
        }),

        getChatToken: build.query<TChatTokenResponse, string>({
            query: (roomId) => ({
                url: `${ENDPOINT}/${roomId}/token`,
                method: "GET"
            }),
        }),

        getChatSuggestions: build.query<TChatSuggestion[], string>({
            query: (roomId) => ({
                url: `${ENDPOINT}/${roomId}/suggestions`,
                method: "GET",
            }),
            transformResponse: (raw: TChatSuggestion[]) => raw,
        }),

        getChatMessages: build.query<
            {messages: TChatMessage[], nextPageUrl: string | null},
            {id: string, params?: TQueryAPI}
        >({
            query: ({id, params}) => ({
                url: `${ENDPOINT}/${id}/messages`,
                method: "GET",
                params,
            }),
            transformResponse: (raw: TReponsePaging<TChatMessage> & {meta: {nextPageUrl?: string}}) => {
                const nextPageUrl = raw.nextPageUrl ?? null
                let messages = (raw.data ?? []).map((message: any) => {
                    return {
                        ...message,
                        role:    message.assistantId ? 'bot'
                            :  (message.userId)     ? 'user'
                            : (message.adminId) ? 'staff'
                                : 'system',
                        userName: message.userName ?? '',
                        sentAt:   message.createdAt
                            ? new Date(message.createdAt).getTime()
                            : undefined,
                        avatarUrl: message.avatarUrl ?? '',
                    }
                })

                return {
                    messages : messages.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()),
                    nextPageUrl: nextPageUrl
                }
            }
        }),

        createChatBotMessage: build.mutation<TChatMessage, {roomId: string; content: string}>({
            query: ({ roomId, content }) => ({
                url: `${ENDPOINT}/${roomId}/messages`,
                method: "POST",
                data: { content },
            }),
        }),

        clearChatMessages: build.mutation<void, string>({
            query: (id) => ({
                url: `${ENDPOINT}/${id}/messages`,
                method: "DELETE",
            }),
        }),

        takeOverChatRoom: build.mutation<{message: string}, {roomId: string}>({
            query: ({roomId}) => ({
                url: `${ENDPOINT}/${roomId}/takeover`,
                method: 'POST'
            })
        }),

        stopTakingOverChatRoom: build.mutation<{message: string}, {roomId: string}>({
            query: ({roomId}) => ({
                url: `${ENDPOINT}/${roomId}/stopTakeover`,
                method: 'POST'
            })
        }),

        getChatRoomById: build.query<TChatBotRoom, string>({
            query: (roomId) => ({
                url: `${ENDPOINT}/rooms/${roomId}`,
                method: 'GET',
            }),
            transformResponse: (raw: TChatBotRoom) => raw,
        }),

        annotateChatMessage: build.mutation<TChatMessage, {roomId: string; originalMessageId: string; content: string}>({
            query: ({roomId, originalMessageId, content}) => ({
                url: `${ENDPOINT}/${roomId}/annotateMessages/${originalMessageId}`,
                method: "POST",
                data: { content }
            }),
            transformResponse: (raw: TChatMessage) => ({
                ...raw,
                role: "system",
            })
        })
    })
})

export const {
    useCreateChatBotMessageMutation,
    useClearChatMessagesMutation,
    useGetChatMessagesQuery,
    useAnnotateChatMessageMutation,
    useListChatRoomsQuery,
    useLazyGetChatMessagesQuery,
    useTakeOverChatRoomMutation,
    useStopTakingOverChatRoomMutation,
    useLazyListChatRoomsQuery,
    useGetChatRoomByIdQuery,
    useLazyGetChatRoomQuery,
} = chatBotService
