type TOrder = TAppModel & {

    shopifyId: string,

    name: string,
    email: string,

    zurnoStatus: string,

    status: string,
    financialStatus: string,
    fulfillmentStatus: string,

    fulfilledAt: Date,

    orderDetailsCount: number,
    subtotalPrice: number,
    totalDiscounts: number,
    totalShipping: number,
    totalTax: number,
    totalPrice: number,
    currentTotalPrice: number,

    currency: string,

    cancelledAt: Date,
    closedAt: Date,

    userId: string,
    user: TUser,

    shippingId: string,
    shipping: TAddress

    billingId: string,
    billing: TAddress,

    customerId: string,

    orderDetails: TOrderDetail[]

    fulfillments: TFulfillment[]
}