type TProduct = {
  id: string;

  shopifyProductId: string
  title: string
  description: string | null
  handle: string
  image: {
    src: string;
  }

  status: 'active' | 'archived' | 'draft'
  publishedAt: string | null

  images: any[]

  collections: TCollection[]
  variants: TProductVariant[]
  options: TProductOptions[]

  category: TProductCategory | null
  vendor: TProductVendor | null
  productType: TProductType | null
  tags: TProductTag[]

  channels: TChannel[]

  createdAt: string;
  updatedAt: string;
  deletedAt: string;
  sendType: string;

  pendingChanges: TProduct
  pendingApproval: number
};
