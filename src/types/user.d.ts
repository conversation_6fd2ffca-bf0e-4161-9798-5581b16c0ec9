type TUser = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullname: string;

  phone: string;
  gender: string;
  birthday: Date;

  mile: string;
  latitude: string;
  longitude: string;

  avatar: string;
  avatarUrl: string;
  friendlyUrl: string;
  avatarId: string;
  avatarMedia: TMedia;

  timezone: string;
  stripeId: string;
  deviceToken: string;

  defaultAddressId: string;

  shopifyCustomerId: string;

  rewardPoints: number;
  noDiscount: number;

  loginCode: string;
  verifiedAt: Date;
  loginCodeExpiredAt: Date;
  lastLoginAt: Date;
  createdAt: Date;
  updatedAt: Date;

  posts: TPost[];
  stores: TStore[];

  active: number;

  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;

  receivePostNotifications: boolean;
  vendorId: string;
};
