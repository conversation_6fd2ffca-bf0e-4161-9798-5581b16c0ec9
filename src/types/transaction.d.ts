type TTransaction = TAppModel & {
  orderId: string;
  order: TOrder;
  
  source: 'PAYPAL' | 'AUTHORIZE_NET' | 'CASH' | 'OTHER';
  status: 'COMPLETED' | 'REFUNDED' | 'CAPTURED' | 'VOIDED';
  
  amount: number;
  currency: string;
  
  payerEmail: string;
  payerName: string;
  
  transactionId: string;
  authorizationId: string;
  
  processedAt: Date;
  refundedAt?: Date;
  
  refundAmount?: number;
  refundReason?: string;
  
  metadata?: Record<string, any>;
};

type TTransactionStats = {
  total: number;
  completed: number;
  refunded: number;
  captured: number;
  voided: number;
};

type TTransactionQuery = TQueryAPI & {
  source?: 'PAYPAL' | 'AUTHORIZE_NET' | 'CASH' | 'OTHER';
  status?: 'COMPLETED' | 'REFUNDED' | 'CAPTURED' | 'VOIDED';
  orderId?: string;
  payerEmail?: string;
};

type TTransactionRefundRequest = {
  amount?: number;
  reason?: string;
}; 