import { FC, Fragment, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Card, Button, Alert, Row, Col } from "react-bootstrap";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import { useEffect } from "react";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import VendorPayout from "./vendor-payout";
import VendorSummaryCard from "./summary/vendor-summary-card";
import { useLazyGetVendorByIdQuery } from "../../../services/vendors";
import { getAllErrorMessages } from "../../../utils/errors";
import VendorDetailEarningsCard from "./vendor-detail-earnings-card";

const VendorDetail: FC = () => {
  const { id: vendorId } = useParams<{ id?: string }>();

  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [vendor, setVendor] = useState<TVendor>();

  const navigate = useNavigate();
  const [getVendor] = useLazyGetVendorByIdQuery();

  const loadVendor = (vendorId: string) => {
    setIsLoading(true);
    setErr({});
    getVendor(vendorId)
      .unwrap()
      .then((res) => {
        setVendor(res);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  useEffect(() => {
    if (vendorId) {
      loadVendor(vendorId);
    }
  }, []);

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack
            title="Vendor Details"
            route=""
          />
          <div className="px-4 justify-content-end">
            <Button
              hidden={!hasPermission(ACTION.UPDATE, RESOURCE.VENDOR)}
              variant="primary-light m-2"
              onClick={() => navigate('edit')}
            >
              Edit <i className="bi bi-pencil ms-2"></i>
            </Button>
          </div>
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Row>
            <Col xl={4}>
              <p>
                <strong>Company Name:</strong> {vendor?.companyName}
              </p>
              <p>
                <strong>Brand Name:</strong> {vendor?.brandName}
              </p>
              <p>
                <strong>Website:</strong> {vendor?.website}
              </p>
              <p>
                <strong>EIN:</strong> {vendor?.ein}
              </p>
            </Col>
            <Col xl={4}>
              <p>
                <strong>Contact Name:</strong> {vendor?.contactName}
              </p>
              <p>
                <strong>Phone:</strong> {vendor?.phone}
              </p>
              <p>
                <strong>Email:</strong> {vendor?.email}
              </p>
              <p>
                <strong>Warehouse:</strong> {vendor?.warehouse?.name}
              </p>
            </Col>
            <Col xl={4}>
              {vendor?.address1 && (
                <p>
                  <strong>Address:</strong>
                  {`${vendor?.address1} ${vendor?.address2}, ${vendor?.city}, ${vendor?.state} ${vendor?.zipCode}, ${vendor?.country}`}
                </p>
              )}
              <p>
                <strong>Registration Status:</strong> {
                  vendor?.registrationStatus === EApprovalStatus.PENDING ? (
                    <span className="badge bg-warning-transparent">Pending</span>
                  ) : vendor?.registrationStatus === EApprovalStatus.APPROVED ? (
                    <span className="badge bg-success text-white">Approved</span>
                  ) : (
                    <span className="badge bg-danger text-white">Rejected</span>
                  )
                }
              </p>
              {
                vendor?.registrationStatus === EApprovalStatus.REJECTED &&
                <p>
                  <strong>Rejection Reason:</strong> {vendor?.rejectionReason}
                </p>
              }
              <p>
                <strong>Register At:</strong> {vendor?.createdAt ? new Date(vendor?.createdAt).toLocaleString() : ""}
              </p>
              <p>
                <strong>Last Update:</strong> {vendor?.updatedAt ? new Date(vendor?.updatedAt).toLocaleString() : ""}
              </p>
              <p>
                <strong>Documents:</strong>
              </p>
            </Col>
          </Row>

        </Card.Body>
      </Card>

      <VendorSummaryCard
        vendorId={vendor?.id ?? ''}
        setIsLoading={setIsLoading}
        setErr={setErr}
      />

      {vendor && <VendorDetailEarningsCard vendor={vendor} />}

      {vendor && <VendorPayout vendor={vendor} />}
    </Fragment>
  );
};

export default VendorDetail;