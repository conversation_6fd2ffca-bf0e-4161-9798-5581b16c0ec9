import { FC, Fragment, useCallback, useEffect, useState } from "react";
import {
  <PERSON>ton,
  Card,
  Col,
  Form,
  InputGroup,
  OverlayTrigger,
  Pagination,
  Row,
  Table,
  Tooltip,
} from "react-bootstrap";
import { useNavigate } from "react-router-dom";

import { format } from "date-fns";
import debounce from "lodash/debounce";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import deleteSweetAlert from "../../../../components/sweet-alerts/delete_swal";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import {
  useLazyListSalonConstructionServiceQuery,
  useDeleteSalonConstructionServiceByIdMutation,
  useLazyGetSalonConstructionServiceStatsQuery,
} from "../../../../services/marketing/salon-construction-service";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import {
  BUDGET_RANGE_LABELS,
  SERVICE_INTEREST_LABELS,
  ESalonConstructionServiceStatus,
  EBudgetRange,
} from "../../../../utils/constant/salon-construction-service";

interface ManagementSalonConstructionServiceProps {}

const ManagementSalonConstructionService: FC<
  ManagementSalonConstructionServiceProps
> = () => {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [totalPages, setTotalPages] = useState(20);

  const [search, setSearch] = useState<string>("");
  const [status, setStatus] = useState<ESalonConstructionServiceStatus | "all">(
    "all"
  );
  const [budgetRange, setBudgetRange] = useState<EBudgetRange | "all">("all");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");

  const [isInitial, setIsInitial] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>();

  const [trigger] = useLazyListSalonConstructionServiceQuery();
  const [deleteById] = useDeleteSalonConstructionServiceByIdMutation();
  const [getStats] = useLazyGetSalonConstructionServiceStatsQuery();

  const [signups, setSignups] = useState<
    TReponsePaging<TSalonConstructionServiceSignup>
  >([]);
  const [stats, setStats] = useState<{
    total: number;
    pending: number;
    approved: number;
    rejected: number;
  } | null>(null);

  const loadData = (
    page: number,
    search?: string,
    status?: ESalonConstructionServiceStatus | "all",
    budgetRange?: EBudgetRange | "all",
    startDate?: string,
    endDate?: string
  ) => {
    setIsLoading(true);

    const params: TSalonConstructionServiceListParams = {
      page,
      limit,
    };

    if (search) params.search = search;
    if (status && status !== "all") params.status = status;
    if (budgetRange && budgetRange !== "all") params.budgetRange = budgetRange;
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;

    trigger(params)
      .then((res) => {
        setTotalPages(res?.data?.meta?.lastPage);
        setSignups(res.data?.data || []);
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setIsInitial(false);
        setIsLoading(false);
      });
  };

  const loadStats = () => {
    getStats()
      .then((res) => {
        setStats(res.data || null);
      })
      .catch((error) => {
        console.log("Error loading stats:", error);
      });
  };

  useEffect(() => {
    loadData(page, search, status, budgetRange, startDate, endDate);
    loadStats();
  }, [page, limit]);

  const debouncedHandleInputSearchFilter = useCallback(
    debounce((value) => {
      loadData(1, value?.search, status, budgetRange, startDate, endDate);
    }, 500),
    [status, budgetRange, startDate, endDate]
  );

  useEffect(() => {
    const query = {
      search: search,
    };
    if (!isInitial) {
      debouncedHandleInputSearchFilter(query);
    }
  }, [search]);

  useEffect(() => {
    if (!isInitial) {
      loadData(1, search, status, budgetRange, startDate, endDate);
    }
  }, [status, budgetRange, startDate, endDate]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleDeleteClick = (signupId: string) => {
    deleteSweetAlert({
      id: signupId,
      deleteAction: () => deleteById(signupId),
      prepareAction: () => setIsLoading(true),
      finishAction: () => {
        loadData(page, search, status, budgetRange, startDate, endDate);
        loadStats();
      },
      finalAction: () => setIsLoading(false),
    });
  };

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Salon Construction Service Management"
                route=""
              ></CardHeaderWithBack>
            </Card.Header>
            <Card.Body className="overflow-auto">
              <div className="app-container">
                {stats && (
                  <Row className="mb-4">
                    <Col md={3}>
                      <Card className="bg-primary-transparent h-100">
                        <Card.Body className="text-center d-flex flex-column justify-content-center" style={{ minHeight: '100px' }}>
                          <h4 className="mb-2">{stats.total}</h4>
                          <p className="mb-0 text-muted">Total Signups</p>
                        </Card.Body>
                      </Card>
                    </Col>
                    <Col md={3}>
                      <Card className="bg-warning-transparent h-100">
                        <Card.Body className="text-center d-flex flex-column justify-content-center" style={{ minHeight: '100px' }}>
                          <h4 className="mb-2">{stats.pending}</h4>
                          <p className="mb-0 text-muted">Pending</p>
                        </Card.Body>
                      </Card>
                    </Col>
                    <Col md={3}>
                      <Card className="bg-success-transparent h-100">
                        <Card.Body className="text-center d-flex flex-column justify-content-center" style={{ minHeight: '100px' }}>
                          <h4 className="mb-2">{stats.approved}</h4>
                          <p className="mb-0 text-muted">Approved</p>
                        </Card.Body>
                      </Card>
                    </Col>
                    <Col md={3}>
                      <Card className="bg-danger-transparent h-100">
                        <Card.Body className="text-center d-flex flex-column justify-content-center" style={{ minHeight: '100px' }}>
                          <h4 className="mb-2">{stats.rejected}</h4>
                          <p className="mb-0 text-muted">Rejected</p>
                        </Card.Body>
                      </Card>
                    </Col>
                  </Row>
                )}

                <Row className="mb-3">
                  <Col md={3}>
                    <Form.Group>
                      <Form.Label>Search</Form.Label>
                      <InputGroup>
                        <Form.Control
                          type="text"
                          value={search}
                          onChange={(e) => setSearch(e.target.value)}
                          placeholder="Search by name, email, business..."
                        />
                        <Button variant="light" onClick={() => setSearch("")}>
                          <i className="ri-close-line"></i>
                        </Button>
                      </InputGroup>
                    </Form.Group>
                  </Col>
                  <Col md={2}>
                    <Form.Group>
                      <Form.Label>Status</Form.Label>
                      <Form.Select
                        value={status}
                        onChange={(e) =>
                          setStatus(
                            e.target.value as
                              | ESalonConstructionServiceStatus
                              | "all"
                          )
                        }
                      >
                        <option value="all">All Status</option>
                        <option value={ESalonConstructionServiceStatus.PENDING}>
                          Pending
                        </option>
                        <option
                          value={ESalonConstructionServiceStatus.APPROVED}
                        >
                          Approved
                        </option>
                        <option
                          value={ESalonConstructionServiceStatus.REJECTED}
                        >
                          Rejected
                        </option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={2}>
                    <Form.Group>
                      <Form.Label>Budget Range</Form.Label>
                      <Form.Select
                        value={budgetRange}
                        onChange={(e) =>
                          setBudgetRange(e.target.value as EBudgetRange | "all")
                        }
                      >
                        <option value="all">All Budgets</option>
                        <option value={EBudgetRange.RANGE_30_50K}>
                          $30-50K
                        </option>
                        <option value={EBudgetRange.RANGE_50_100K}>
                          $50-100K
                        </option>
                        <option value={EBudgetRange.RANGE_100_250K}>
                          $100-250K
                        </option>
                        <option value={EBudgetRange.RANGE_250_500K}>
                          $250-500K
                        </option>
                        <option value={EBudgetRange.RANGE_500K_PLUS}>
                          $500K+
                        </option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={2}>
                    <Form.Group>
                      <Form.Label>Start Date</Form.Label>
                      <Form.Control
                        type="date"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={2}>
                    <Form.Group>
                      <Form.Label>End Date</Form.Label>
                      <Form.Control
                        type="date"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={1}>
                    <Form.Group>
                      <Form.Label>&nbsp;</Form.Label>
                      <div>
                        <Button
                          variant="secondary"
                          onClick={() => {
                            setSearch("");
                            setStatus("all");
                            setBudgetRange("all");
                            setStartDate("");
                            setEndDate("");
                          }}
                        >
                          Clear
                        </Button>
                      </div>
                    </Form.Group>
                  </Col>
                </Row>
                <Table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th>Business Name</th>
                      <th>Owner Name</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Salon Address</th>
                      <th>Service Interest</th>
                      <th>Budget Range</th>
                      <th>Preferred Start Date</th>
                      <th>Status</th>
                      <th>PDF</th>
                      <th>Created Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {signups.map((signup: TSalonConstructionServiceSignup) => (
                      <Fragment key={signup.id}>
                        <ReadOnlyRow
                          signup={signup}
                          err={err}
                          setErr={setErr}
                          setIsLoading={setIsLoading}
                          handleDeleteClick={handleDeleteClick}
                        />
                      </Fragment>
                    ))}
                  </tbody>
                </Table>
                <div className="d-flex justify-content-center my-3">
                  {" "}
                  <Pagination>
                    <Pagination.First onClick={() => handlePageChange(1)} />
                    <Pagination.Prev
                      onClick={() => handlePageChange(Math.max(page - 1, 1))}
                    />

                    {[...Array(totalPages).keys()]
                      .slice(
                        Math.max(page - 2, 0),
                        Math.min(page + 3, totalPages)
                      )
                      .map((p) => (
                        <Pagination.Item
                          key={p + 1}
                          active={p + 1 === page}
                          onClick={() => handlePageChange(p + 1)}
                        >
                          {p + 1}
                        </Pagination.Item>
                      ))}

                    <Pagination.Next
                      onClick={() =>
                        handlePageChange(Math.min(page + 1, totalPages))
                      }
                    />
                    <Pagination.Last
                      onClick={() => handlePageChange(totalPages)}
                    />
                  </Pagination>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

const ReadOnlyRow = ({ signup, handleDeleteClick }: any) => {
  const navigate = useNavigate();

  return (
    <tr>
      <td
        className="wd-5p text-center"
        style={{
          maxWidth: "150px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
        title={signup.businessName}
      >
        {signup.businessName}
      </td>
      <td
        className="wd-5p text-center"
        style={{
          maxWidth: "120px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
        title={signup.fullName}
      >
        {signup.fullName}
      </td>
      <td
        className="wd-5p text-center"
        style={{
          maxWidth: "150px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
        title={signup.emailAddress}
      >
        <a href={`mailto:${signup.emailAddress}`}>{signup.emailAddress}</a>
      </td>
      <td>
        <a href={`tel:${signup.phoneNumber}`}>{signup.phoneNumber}</a>
      </td>
      <td
        className="wd-5p text-center"
        style={{
          maxWidth: "200px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
        title={signup.salonAddress}
      >
        {signup.salonAddress}
      </td>
      <td
        className="wd-5p text-center"
        style={{
          maxWidth: "200px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
        title={signup.serviceInterest
          .map(
            (interest: string) =>
              SERVICE_INTEREST_LABELS[
                interest as keyof typeof SERVICE_INTEREST_LABELS
              ]
          )
          .join(", ")}
      >
        {signup.serviceInterest
          .map(
            (interest: string) =>
              SERVICE_INTEREST_LABELS[
                interest as keyof typeof SERVICE_INTEREST_LABELS
              ]
          )
          .join(", ")}
      </td>
      <td>{BUDGET_RANGE_LABELS[signup.budgetRange]}</td>
      <td>
        {signup.preferredStartDate
          ? format(new Date(signup.preferredStartDate), "yyyy/M/d")
          : "Not specified"}
      </td>
      <td>
        {signup.status == "pending" ? (
          <span className="badge bg-warning-transparent">Pending</span>
        ) : signup.status == "approved" ? (
          <span className="badge bg-success-transparent">Approved</span>
        ) : signup.status == "rejected" ? (
          <span className="badge bg-danger-transparent">Rejected</span>
        ) : null}
      </td>
      <td className="text-center">
        {signup.pdfFile ? (
          <OverlayTrigger
            placement="top"
            overlay={<Tooltip>Download PDF</Tooltip>}
          >
            <Button
              variant="info-light"
              size="sm"
              onClick={() => {
                const url = `${
                  import.meta.env.VITE_APP_API_URL
                }/v1/salon-construction-service/${signup.id}/pdf`;
                window.open(url, "_blank");
              }}
            >
              <i className="ri-file-pdf-line"></i>
            </Button>
          </OverlayTrigger>
        ) : (
          <span className="text-muted">No PDF</span>
        )}
      </td>
      <td>
        {signup.createdAt
          ? format(new Date(signup.createdAt), "yyyy/M/d h:mm aa")
          : ""}
      </td>
      <td>
        <OverlayTrigger placement="top" overlay={<Tooltip>View</Tooltip>}>
          <Button
            hidden={
              !hasPermission(ACTION.READ, RESOURCE.SALON_CONSTRUCTION_SERVICE)
            }
            variant="info-light"
            className="btn btn-info-light btn-sm ms-2"
            onClick={() => navigate(`details/${signup.id}`)}
          >
            <span className="ri-eye-line fs-14"></span>
          </Button>
        </OverlayTrigger>

        <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
          <Button
            hidden={
              !hasPermission(ACTION.DELETE, RESOURCE.SALON_CONSTRUCTION_SERVICE)
            }
            variant="primary-light"
            className="btn btn-danger-light btn-sm ms-2"
            onClick={() => handleDeleteClick(signup.id, signup.businessName)}
          >
            <span className="ri-delete-bin-7-line fs-14"></span>
          </Button>
        </OverlayTrigger>
      </td>
    </tr>
  );
};

export default ManagementSalonConstructionService;
