import React, { FC, Fragment, useEffect, useState } from "react";
import { Badge, Button, Card, Carousel, Col, Form, Modal, OverlayTrigger, Row, Table, Tooltip } from "react-bootstrap";

import { Link } from "react-router-dom";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import deleteSweetAlert from "../../../../components/sweet-alerts/delete_swal";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import { useLazyDeleteOrderByIdQuery, useLazyListOrdersQuery } from "../../../../services/order";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import { debounce } from "lodash";
import moment from "moment";
import { currencySymbol } from "../../../../utils/constant/currency";

interface ManagementOrderProps { }

const ManagementOrder: FC<ManagementOrderProps> = () => {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [lastPage, setLastPage] = useState(20);
  const [total, setTotal] = useState(20);
  const [search, setSearch] = useState("");
  const setDebouncedSearch = debounce((value) => setSearch(value), 1000)

  const [isLoading, setIsLoading] = useState(false);

  const [trigger] = useLazyListOrdersQuery();
  const [deleteById] = useLazyDeleteOrderByIdQuery();

  const [orders, setOrders] = useState<TOrder[]>([]);

  useEffect(() => {
    setIsLoading(true);
    trigger({ page, limit, search })
      .unwrap()
      .then((res) => {
        setOrders(res.data || []);
        setLastPage(res?.meta?.lastPage);
        setTotal(res?.meta?.total);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [page, limit, search]);

  const handleDeleteClick = (order: TOrder) => {
    deleteSweetAlert({
      id: order.id,
      deleteAction: deleteById,
      prepareAction: (() => setIsLoading(true)),
      // finishAction: (() => loadData(page)),
      finalAction: (() => setIsLoading(false)),
    })
  };

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Order Management"
                route=""
              ></CardHeaderWithBack>
            </Card.Header>
            <Card.Body className="overflow-auto">
              <div className="app-container">
                <Form.Group className="mb-3">
                  <Form.Control
                    type="search"
                    placeholder="Search..."
                    onChange={(e) => setDebouncedSearch(e.target.value)}
                  />
                </Form.Group>

                <Table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th className="text-center">Order</th>
                      <th className="text-center">Details</th>
                      <th className="text-center">Customer</th>
                      <th className="text-center">Payment</th>
                      <th className="text-center">Fulfillment</th>
                      <th className="text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {orders.map((order: TOrder) => (
                      <Fragment key={order.id}>
                        <ReadOnlyRow
                          order={order}
                          handleDeleteClick={handleDeleteClick}
                        />
                      </Fragment>
                    ))}
                  </tbody>
                </Table>
                <PaginationBar
                  page={page}
                  setPage={setPage}
                  lastPage={lastPage}
                  total={total}
                />
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

const ReadOnlyRow = ({
  order,
  handleDeleteClick,
}: any) => {

  const [orderNameHovered, setOrderNameHovered] = useState(false)
  const [customerNameHovered, setCustomerNameHovered] = useState(false)

  const [orderDetailsModalShown, setOrderDetailsModalShown] = useState(false)

  const [paymentDetailsModalShown, setPaymentDetailsModalShown] = useState(false)

  const [shipmentAddressModalShown, setShipmentAddressModalShown] = useState(false)

  // const [trackingInfo, setTrackingInfo] = useState<{
  //   trackingCompany: string,
  //   trackings: {
  //     id: number,
  //     number: string,
  //     url: string
  //   }[]
  // } | null>(null)
  // useEffect(() => {
  //   if (order) {
  //     const numbers = order.fulfillment?.trackingNumbers.split(',')
  //     const urls = order.fulfillment?.trackingUrls.split(',')

  //     const longestArray = (numbers?.length || 0) > (urls?.length || 0) ? numbers : urls

  //     const trackings: { id: number, number: string, url: string }[] = []
  //     for (let i = 0; i < (longestArray?.length || 0); i++) {
  //       trackings.push({ id: Date.now() + i, number: numbers[i] || '', url: urls[i] || '' })
  //     }

  //     setTrackingInfo({
  //       trackingCompany: order.fulfillment?.trackingCompany || '',
  //       trackings,
  //     })
  //   }
  // }, [order])

  return (
    <Fragment>
      <tr>
        <td className="text-center">
          <div>
            <Link
              to={order.id}
              className={`text-${orderNameHovered ? 'info text-decoration-underline' : 'primary'}`}
              onMouseEnter={() => setOrderNameHovered(true)}
              onMouseLeave={() => setOrderNameHovered(false)}
            >
              {order.name}
            </Link>
          </div>
          <div>
            {moment(order.createdAt).format('YYYY-MM-DD')}
          </div>
          <div>
            {moment(order.createdAt).format('HH:mm:ss')}
          </div>
          <div>
            <Badge className="text-capitalize rounded-pill bg-primary-transparent">
              {order.zurnoStatus}
            </Badge>
          </div>
        </td>
        <td className="text-center" style={{ width: 300 }}>
          <div className="text-primary mb-1">
            <span
              style={{ cursor: 'pointer' }}
              onClick={() => setOrderDetailsModalShown(true)}
            >
              {order.orderDetailsCount} Product{order.orderDetailsCount > 1 ? 's' : ''}
            </span>
          </div>
          <div>
            <Carousel indicators={false}>
              {order.orderDetails.map((details, index) => (
                <Carousel.Item key={index}>
                  <div className="avatar avatar-xl bg-dark-transparent">
                    {
                      details.variant?.image?.src
                        ?
                        <img
                          src={details.variant?.image?.src || ''}
                          className=""
                        />
                        : "IMG"
                    }
                  </div>
                  <div>
                    <div>
                      {details.variant.title}
                    </div>
                    <div>
                      {details.variant.sku} - Quantities: {details.quantity}
                    </div>
                  </div>
                </Carousel.Item>
              ))}
            </Carousel>
          </div>
        </td>
        <td className="text-center">
          <div>
            <Link
              to={`/managements-users/details/${order.user?.id}`}
              className={`text-${customerNameHovered ? 'info text-decoration-underline' : 'primary'}`}
              onMouseEnter={() => setCustomerNameHovered(true)}
              onMouseLeave={() => setCustomerNameHovered(false)}
            >
              {order.user?.fullname}
            </Link>
          </div>
          <div>
            {order.user?.email}
          </div>
          <div>
            {order.user?.phone}
          </div>
        </td>
        <td className="text-center">
          <div className="text-primary">
            <span
              style={{ cursor: 'pointer' }}
              onClick={() => setPaymentDetailsModalShown(true)}
            >
              Value
            </span>
          </div>
          <div>
            {currencySymbol[order.currency]} {order.totalPrice}
          </div>
          <div className="text-uppercase">
            {order.financialStatus}
          </div>
        </td>
        <td>
          {order.fulfillmentStatus == 'fulfilled' ? (
            <span className="badge bg-success-transparent rounded-pill">
              Fulfilled
            </span>
          ) : order.fulfillmentStatus == 'partially_fulfilled' ? (
            <span className="badge bg-warning-transparent rounded-pill">
              Partially Fulfilled
            </span>
          ) : order.fulfillmentStatus == 'unfulfilled' ? (
            <span className="badge bg-danger-transparent rounded-pill">
              Unfulfilled
            </span>
          ) : <>
            {order.fulfillmentStatus}
          </>
          }
        </td>
        <td className="text-center">
          <OverlayTrigger placement="top" overlay={<Tooltip>Edit</Tooltip>}>
            <Link to={order.id}>
              <Button
                variant="primary-light"
                className="btn btn-warning-light btn-sm ms-2"
                hidden={!hasPermission(ACTION.UPDATE, RESOURCE.ORDER)}
              >
                <span className="ri-edit-line fs-14"></span>
              </Button>
            </Link>
          </OverlayTrigger>

          <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
            <Button
              variant="primary-light"
              className="btn btn-danger-light btn-sm ms-2"
              hidden={!hasPermission(ACTION.DELETE, RESOURCE.ORDER)}
              onClick={() => handleDeleteClick(order)}
            >
              <span className="ri-delete-bin-7-line fs-14"></span>
            </Button>
          </OverlayTrigger>
        </td>
      </tr>

      <OrderDetailsModal
        show={orderDetailsModalShown}
        onHide={() => setOrderDetailsModalShown(false)}
        order={order}
      />

      <PaymentDetailsModal
        show={paymentDetailsModalShown}
        onHide={() => setPaymentDetailsModalShown(false)}
        order={order}
      />

      <ShipmentAddressModal
        show={shipmentAddressModalShown}
        onHide={() => setShipmentAddressModalShown(false)}
        order={order}
      />

    </Fragment>
  );
};

const OrderDetailsModal = ({
  show,
  onHide,

  order,
}: any) => {

  const [productNameHovered, setProductNameHovered] = useState(false)

  return (
    <Fragment>
      <Modal
        show={show}
        onHide={() => { onHide?.() }}

        centered
        size='xl'
      >
        <Modal.Header closeButton>
          <Modal.Title>
            Order {order.order?.name}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="overflow-auto">
          <Table className="table table-bordered text-nowrap border-bottom mb-3">
            <thead>
              <tr>
                <th className="text-center">Image</th>
                <th className="text-center">Product</th>
                <th className="text-center">Quantity</th>
                <th className="text-center">Price</th>
                <th className="text-center">Value</th>
              </tr>
            </thead>
            <tbody>
              {order.orderDetails.map((details, index) => (
                <tr key={index}>
                  <td className="text-center">
                    <div className="avatar avatar-xxl bg-dark-transparent">
                      {
                        details.variant?.image?.src
                          ?
                          <img
                            src={details.variant?.image?.src || ''}
                            className=""
                          />
                          : "IMG"
                      }
                    </div>
                  </td>
                  <td className="text-center">
                    <div>{details.variant?.sku}</div>
                    <div>{details.variant?.barcode}</div>
                    <div>{details.variant?.title}</div>
                    <div>
                      <Link
                        to={`/products/${details.variant?.productId}`}
                        className={`text-${productNameHovered ? 'info text-decoration-underline' : "primary"}`}
                        onMouseEnter={() => setProductNameHovered(true)}
                        onMouseLeave={() => setProductNameHovered(false)}
                      >
                        {details.variant?.product?.title}
                      </Link>
                    </div>
                  </td>
                  <td className="text-center">{details.quantity}</td>
                  <td className="text-center">{currencySymbol[order.currency]} {details.price}</td>
                  <td className="text-center">{currencySymbol[order.currency]} {details.quantity * details.price}</td>
                </tr>
              ))}
            </tbody>
          </Table>
        </Modal.Body>
      </Modal>
    </Fragment>
  )
}

const PaymentDetailsModal = ({
  show,
  onHide,

  order,
}: any) => {

  return (
    <Fragment>
      <Modal
        show={show}
        onHide={() => { onHide?.() }}

        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>
            Order {order.order?.name}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row>
            <Col>
              <div className="fw-bold">Financial Status</div>
              <div className="fw-bold">Total Discounts</div>
              <div className="fw-bold">Total Tax</div>
              <div className="fw-bold">Total Shipping</div>
              <div className="fw-bold">Subtotal Price</div>
              <div className="fw-bold">Total Price</div>
              <div className="fw-bold">Current Total Price</div>
            </Col>
            <Col>
              <div className="text-capitalize">{order.financialStatus}</div>
              <div>{order.totalDiscounts}</div>
              <div>{order.totalTax}</div>
              <div>{order.totalShipping}</div>
              <div>{order.subtotalPrice}</div>
              <div>{order.totalPrice}</div>
              <div>{order.currentTotalPrice}</div>
            </Col>
          </Row>
        </Modal.Body>
      </Modal>
    </Fragment>
  )
}

const ShipmentAddressModal = ({
  show,
  onHide,

  order,
}: any) => {

  return (
    <Fragment>
      <Modal
        show={show}
        onHide={() => { onHide?.() }}

        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>
            Order {order.order?.name}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                First Name
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.firstName}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Last Name

              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.lastName}
              </div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Name
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.name}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Company
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.company}
              </div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Address 1
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.address1}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Address 2
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.address2}
              </div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                City
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.city}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Zip
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.zip}
              </div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Province
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.province}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Country
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.country}
              </div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Province Code
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.provinceCode}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Country Code
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.countryCode}
              </div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Latitude
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.latitude}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label>
                Longitude
              </Form.Label>
              <div className="text-muted">
                {order.order?.shipping?.longitude}
              </div>
            </Col>
          </Row>
        </Modal.Body>
      </Modal>
    </Fragment>
  )
}

export default ManagementOrder;
