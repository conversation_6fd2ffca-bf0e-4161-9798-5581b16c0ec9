import React, { FC, Fragment, useEffect, useState } from "react"
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, OverlayTrigger, Row, Table, Tooltip } from "react-bootstrap";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { hasPermission } from "../../../utils/authorization";
import { useNavigate } from "react-router-dom";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import { getAllErrorMessages } from "../../../utils/errors";
import deleteSweetAlert from "../../../components/sweet-alerts/delete_swal";
import {
  useLazyListAffiliateTierQuery,
  useDeleteAffiliateTierByIdMutation
} from "../../../services/affiliation/affiliatie-tier";

interface AffiliateTiersProps { }

const AffiliateTiers: FC<AffiliateTiersProps> = () => {
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(20);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [tiers, setTiers] = useState<TAffiliateTier[]>([]);

  const [trigger] = useLazyListAffiliateTierQuery();
  const [deleteById] = useDeleteAffiliateTierByIdMutation();

  const loadData = (query: TQueryAPI) => {
    setIsLoading(true);
    trigger(query)
      .unwrap()
      .then((res) => {
        setTotalPages(res?.meta?.lastPage);
        setTiers(res.data || []);
        setErr({});
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  useEffect(() => {
    loadData({ page });
  }, [page]);

  const handleDeleteClick = (tierId: string) => {
    deleteSweetAlert({
      id: tierId,
      deleteAction: deleteById,
      prepareAction: () => setIsLoading(true),
      finishAction: () => loadData({ page }),
      finalAction: () => setIsLoading(false),
    });
  }

  const navigate = useNavigate();

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Affiliation Tiers Management"
                route=""
              />
              <div className="px-4 justify-content-end">
                <Button
                  hidden={!hasPermission(ACTION.CREATE, RESOURCE.AFFILIATION)}
                  variant="primary-light"
                  onClick={() => navigate("new")}
                >
                  Add<i className="bi bi-plus-lg ms-2"></i>
                </Button>
              </div>
            </Card.Header>
            <Card.Body>
              <div className="app-container">
                {err?.messages?.map((message: string, index: number) => (
                  <Alert key={index} variant="danger">
                    {message}
                  </Alert>
                ))}

                <Table className="table table-bordered text-nowrap border-bottom" responsive>
                  <thead>
                    <tr>
                      <th className="w-5">Tier</th>
                      <th>Title</th>
                      <th>Required New Customers</th>
                      <th className="w-15">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tiers?.map((tier, index) => (
                      <Fragment key={tier.id}>
                        <ReadOnlyRow
                          tier={tier}
                          index={index + 1}
                          handleDeleteClick={handleDeleteClick}
                        />
                      </Fragment>
                    ))}
                  </tbody>
                </Table>
                <PaginationBar
                  page={page}
                  setPage={setPage}
                  lastPage={totalPages}
                />
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

interface ReadOnlyRowProps {
  tier: TAffiliateTier;
  index: number;
  handleDeleteClick: (id: string) => void;
}

const ReadOnlyRow = ({
  tier,
  handleDeleteClick,
}: ReadOnlyRowProps) => {
  const navigate = useNavigate();

  return (
    <tr>
      <td>{tier.tier}</td>
      <td>{tier.title}</td>
      <td>{tier.requiredNewCustomers}</td>
      <td>
        <OverlayTrigger placement="top" overlay={<Tooltip>Edit</Tooltip>}>
          <Button
            variant="primary-light"
            className="btn btn-warning-light btn-sm ms-2"
            hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION)}
            onClick={() => navigate(tier.id)}
          >
            <span className="ri-edit-line fs-14"></span>
          </Button>
        </OverlayTrigger>

        <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
          <Button
            variant="primary-light"
            className="btn btn-danger-light btn-sm ms-2"
            hidden={!hasPermission(ACTION.DELETE, RESOURCE.AFFILIATION)}
            onClick={() => handleDeleteClick(tier.id)}
          >
            <span className="ri-delete-bin-7-line fs-14"></span>
          </Button>
        </OverlayTrigger>
      </td>
    </tr>
  );
};

export default AffiliateTiers;