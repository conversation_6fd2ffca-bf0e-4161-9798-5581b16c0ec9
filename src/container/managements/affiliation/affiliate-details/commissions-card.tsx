import { FC, Fragment, useEffect, useState } from "react";
import { Accordion, <PERSON><PERSON>, Card, Col, InputGroup, Row, Table } from "react-bootstrap";
import { TAffiliateCommission } from "../../../../types/affiliation/affiliate-commission";
import AffiliateCommissionRow from "../rows/affiliate-commission-row";
import { ErrorType } from "../../../../utils/error_type";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import { useLazyGetCommisisonsQuery, useLazyGetCommissionRateSummaryQuery } from "../../../../services/affiliation/affiliate";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import { getAllErrorMessages } from "../../../../utils/errors";
import ReactDatePicker from "react-datepicker";
import formatUSD from "../../../../utils/currency-formatter";
import CalculateCommissionsButton from "./calculate-commissions-button";

interface CommissionsCardProps {
  affiliateId: string
}

const CommissionsCard: FC<CommissionsCardProps> = ({ affiliateId }) => {
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(20);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<ErrorType>({});
  const [commissions, setCommissions] = useState<TAffiliateCommission[]>([]);
  const [commissionRateSummary, setCommissionRateSummary] = useState<TAffiliateCommissionRateSummary>();
  const [filterDate, setFilterDate] = useState<Date>(new Date());

  const [getCommissions] = useLazyGetCommisisonsQuery();
  const [getCommissionRateSummary] = useLazyGetCommissionRateSummaryQuery();

  useEffect(() => {
    loadCommisisonRateSummary();
    loadCommisison();
  }, [filterDate]);

  useEffect(() => {
    loadCommisison();
  }, [page, filterDate]);

  const loadCommisison = () => {
    const startDate = getStartDate(filterDate);
    const endDate = getEndDate(filterDate);

    setIsLoading(true);
    setErr({});
    getCommissions({
      affiliateId,
      page,
      limit: 10,
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
    })
      .unwrap()
      .then((res) => {
        setTotalPages(res?.meta?.lastPage);
        setCommissions(res.data || []);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const loadCommisisonRateSummary = () => {
    const startDate = getStartDate(filterDate);
    const endDate = getEndDate(filterDate);

    setIsLoading(true);
    setErr({});
    getCommissionRateSummary({
      affiliateId,
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
    })
      .unwrap()
      .then(setCommissionRateSummary)
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const getStartDate = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 0);
  }

  const getEndDate = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0);
  }

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-CA', { year: 'numeric', month: '2-digit', day: '2-digit' }).format(date);
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Card className="custom-card">
        <Card.Header>
          <Card.Title>Commissions</Card.Title>
          <div className="px-4 justify-content-end">
            <Row>
              <Col>
                <CalculateCommissionsButton
                  affiliateId={affiliateId}
                  startDate={formatDate(getStartDate(filterDate))}
                  endDate={formatDate(getEndDate(filterDate))}
                  setIsLoading={setIsLoading}
                  setErr={setErr}
                  onFinish={(success) => {
                    if (success) {
                      loadCommisison();
                    }
                  }} />
              </Col>
              <Col>
                <div className="mt-2">
                  <InputGroup className="input-group">
                    <InputGroup.Text className="input-group-text text-muted">
                      {" "}
                      <i className="ri-time-line"></i>{" "}
                    </InputGroup.Text>
                    <ReactDatePicker
                      selected={filterDate}
                      onChange={(date) => {
                        setFilterDate(date ?? new Date());
                      }}
                      dateFormat="MM/yyyy"
                      showMonthYearPicker
                    />
                  </InputGroup>
                </div>
              </Col>
            </Row>
          </div>
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          {
            (commissionRateSummary) && <Accordion defaultActiveKey={['0']} className="accordion accordion-primary mb-3" alwaysOpen>
              <Accordion.Item eventKey="0">
                <Accordion.Header>Summary</Accordion.Header>
                <Accordion.Body>
                  <Row>
                    <Col xl={4}>
                      <div className="d-flex mb-3">
                        <div className="text-left">
                          <p className="mb-1 text-muted">Total Sales</p>
                          <h5 className="mb-0">{formatUSD(commissionRateSummary.totalSales)}</h5>
                        </div>
                      </div>
                    </Col>
                    {
                      commissionRateSummary.groups.map((group) => (
                        <Col xl={4}>
                          <div className="d-flex mb-3">
                            <div className="text-left">
                              <p className="mb-1 text-muted">{`Fitted Range of ${group.title} Group`}</p>
                              <h5 className="mb-0">{formatUSD(group.from >= 0 ? group.from : 0)} - {group.to >= 0 ? formatUSD(group.to) : <i className="bi bi-infinity" />}</h5>
                            </div>
                          </div>
                          <div className="d-flex mb-3">
                            <div className="text-left">
                              <p className="mb-1 text-muted">Commission Rate</p>
                              <h5 className="mb-0">{group.rate * 100}%</h5>
                            </div>
                          </div>
                        </Col>
                      ))
                    }
                  </Row>
                </Accordion.Body>
              </Accordion.Item>
            </Accordion>
          }

          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <thead>
              <tr>
                <th>Created at</th>
                <th>Amount ($)</th>
                <th className="w-5" style={{ textAlign: "center" }}>Status</th>
                <th colSpan={3} style={{ textAlign: "center" }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {
                commissions.map((commission: TAffiliateCommission) => (
                  <Fragment key={commission.id}>
                    <AffiliateCommissionRow inputCommission={commission} shouldShowAffiliateDetail={false} setErr={setErr} />
                  </Fragment>
                ))
              }
            </tbody>
          </Table>

        </Card.Body>

        <Card.Footer>
          <PaginationBar
            page={page}
            setPage={setPage}
            lastPage={totalPages}
          />
        </Card.Footer>
      </Card>
    </Fragment>
  );
}

export default CommissionsCard;