import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Card, Form, InputGroup, Table } from "react-bootstrap";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { ErrorType } from "../../../utils/error_type";
import { getAllErrorMessages } from "../../../utils/errors";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import Swal from "sweetalert2";
import EditCommissionGroupDialog from "../../../components/dialog/edit-commission-group-dialog";
import AddProductsToCommissionGroupDialog from "../../../components/dialog/add-products-to-commission-group-dialog";
import { EListAction } from "../../../components/enums/list-action";
import { useLazyGetProductsOfCommissionGroupsQuery, useUpdateProductsOfCommissionGroupMutation } from "../../../services/affiliation/affiliatie-tier-commission-group";

interface TierCommissionableGroupProps {
  tierId: string;
  commissionGroup: TAffiliateTierCommissionGroup;
  showDeleteButton: boolean;
  onDeleteGroup: (groupId: string) => void;
}

const TierCommissionableGroup: FC<TierCommissionableGroupProps> = ({ tierId, commissionGroup, showDeleteButton, onDeleteGroup }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [group, setGroup] = useState<TAffiliateTierCommissionGroup>();
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(20);
  const [totalProducts, setTotalProducts] = useState(0);
  const [products, setProducts] = useState<TProduct[]>([]);
  const [err, setErr] = useState<ErrorType>({});
  const [showEditGroupDialog, setShowEditGroupDialog] = useState<boolean>(false);
  const [showAddingProductsDialog, setShowAddingProductsDialog] = useState<boolean>(false);
  const [searchTermFormData, setSearchTermFormData] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([])
  const [expandVariants, setExpandVariants] = useState<Map<TProduct, boolean>>(new Map<TProduct, boolean>());

  const [getProducts] = useLazyGetProductsOfCommissionGroupsQuery();
  const [updateProducts] = useUpdateProductsOfCommissionGroupMutation();

  useEffect(() => {
    setGroup(commissionGroup);
  }, []);

  useEffect(() => {
    if (group) {
      handleGetProducts();
    }
  }, [searchTerm, group, page]);

  const handleGetProducts = () => {
    if (!tierId) {
      console.error('Tier ID is required');
      return;
    }
    if (!group) {
      console.error('Group data is required');
      return;
    }

    setIsLoading(true);
    setErr({});
    getProducts({ groupId: group.id, searchTerm, page, limit: 10 })
      .unwrap()
      .then((res) => {
        setTotalPages(res?.meta?.lastPage);
        setTotalProducts(res?.meta?.total);
        setProducts(res.data || []);

        if (res.data) {
          const newExpandVariants = new Map<TProduct, boolean>();
          for (const product of res.data) {
            newExpandVariants.set(product, false);
          }
          setExpandVariants(newExpandVariants);
        }
      })
      .catch((error) => setErr(getAllErrorMessages(error)))
      .finally(() => setIsLoading(false));
  }

  const handleDeleteGroup = () => {
    Swal.fire({
      title: "Are you sure?",
      html: `Delete ${group?.title || '[Untitled]'} commission group?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          onDeleteGroup(group!.id);
        }
      })
      .catch((error) => {
        Swal.fire("Error!", error, "error");
      })
  }

  const handleSearch = (e) => {
    e.preventDefault();
    setSearchTerm(searchTermFormData);
  }

  const handleClearSearch = () => {
    setSearchTermFormData('');
    setSearchTerm('');
  }

  const handleCheckboxChange = (productId) => {
    setSelectedProductIds((prevSelected) =>
      prevSelected.includes(productId)
        ? prevSelected.filter((rowId) => rowId !== productId)
        : [...prevSelected, productId]
    );
  };

  const handleExpandVariants = (product: TProduct, expand: boolean) => {
    const newExpandVariants = new Map<TProduct, boolean>(expandVariants);
    newExpandVariants.set(product, expand);
    setExpandVariants(newExpandVariants);
  }

  const handleRemoveProduct = (product: TProduct) => {
    if (!tierId) {
      console.error('Unknown tier ID');
      return;
    }
    if (!product) {
      console.error('Product is null');
      return;
    }

    Swal.fire({
      title: "Are you sure?",
      text: `Remove ${product.title} from group?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, remove it!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          setErr({});
          setIsLoading(true);
          updateProducts({
            groupId: group!.id,
            action: EListAction.REMOVE,
            productIds: [product.id],
          })
            .unwrap()
            .then(handleGetProducts)
            .catch((error) => {
              setErr(getAllErrorMessages(error));
            })
            .finally(() => {
              setIsLoading(false);
            });

          Swal.fire("Removed!",
            `${product.title} removed.`,
            "success"
          );
        }
      }
      )
      .catch(() => {
        Swal.fire("Error!", "Something went wrong!", "error");
      });
  }

  const handleRemoveSelectedProducts = () => {
    if (!tierId) {
      console.error('Unknown tier ID');
      return;
    }

    if (selectedProductIds.length === 0) return

    Swal.fire({
      title: "Are you sure?",
      text: `Remove ${selectedProductIds.length} selected products from group?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, remove it!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          setErr({});
          setIsLoading(true);
          updateProducts({
            groupId: group!.id,
            action: EListAction.REMOVE,
            productIds: selectedProductIds,
          })
            .unwrap()
            .then(() => {
              handleGetProducts();
              setSelectedProductIds([])
            })
            .catch((error) => {
              setErr(getAllErrorMessages(error));
            })
            .finally(() => {
              setIsLoading(false);
            });

          Swal.fire("Removed!",
            'Removed selected products',
            "success"
          );
        }
      }
      )
      .catch(() => {
        Swal.fire("Error!", "Something went wrong!", "error");
      });
  }

  const handleRemoveAllProducts = () => {
    if (!tierId) {
      console.error('Unknown tier ID');
      return;
    }

    Swal.fire({
      title: "Are you sure?",
      text: `Remove all products from group?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, remove all!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          setErr({});
          setIsLoading(true);
          updateProducts({
            groupId: group!.id,
            action: EListAction.REMOVE,
            productIds: ['all'],
          })
            .unwrap()
            .then(handleGetProducts)
            .catch((error) => {
              setErr(getAllErrorMessages(error));
            })
            .finally(() => {
              setIsLoading(false);
            });

          Swal.fire('Removed!',
            'All products are removed',
            'success'
          );
        }
      }
      )
      .catch(() => {
        Swal.fire("Error!", "Something went wrong!", "error");
      });
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header className="justify-content-between">
          <Card.Title>{group?.title ?? 'Group'}</Card.Title>
          <div className="px-4 justify-content-end">
            <Button
              hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION)}
              variant="primary-light"
              onClick={() => { setShowEditGroupDialog(true) }}
            >
              <span className="ri-edit-line fs-14" />
            </Button>
            <Button
              hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION) || !showDeleteButton}
              variant="primary-light"
              className="btn btn-danger-light ms-2"
              onClick={handleDeleteGroup}
            >
              <span className="bi bi-x-lg"></span>
            </Button>
          </div>
        </Card.Header>
        <Card.Body>

          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <div className="d-flex">
            <Button
              hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION)}
              variant="primary-light"
              className="m-2"
              onClick={() => { setShowAddingProductsDialog(true) }}
            >
              <i className="bi bi-plus-lg" /> Add Products
            </Button>

            <div className="ms-auto">
              <Button
                hidden={!hasPermission(ACTION.CREATE, RESOURCE.AFFILIATION)}
                variant="primary-light"
                className="btn btn-danger-light m-2"
                onClick={handleRemoveSelectedProducts}
                disabled={selectedProductIds.length === 0}
              >
                <span className="ri-delete-bin-7-line fs-14" /> Remove Selected Products ({selectedProductIds.length})
              </Button>

              <Button
                hidden={!hasPermission(ACTION.CREATE, RESOURCE.AFFILIATION)}
                variant="primary-light"
                className="btn btn-danger-light m-2"
                onClick={handleRemoveAllProducts}
                disabled={products.length === 0}
              >
                <span className="ri-delete-bin-7-line fs-14" /> Remove All Products ({totalProducts})
              </Button>
            </div>
          </div>

          <Form onSubmit={handleSearch}>
            <InputGroup className="mb-3">
              <Form.Control
                type="text"
                placeholder="Search..."
                value={searchTermFormData}
                onChange={(e) => setSearchTermFormData(e.target.value)}
              />
              <Button variant="light" className="btn btn-light btn-sm" onClick={handleClearSearch}>X</Button>
              <Button variant="primary" type="submit">Search</Button>
            </InputGroup>
          </Form>

          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <thead>
              <tr>
                <th className="text-center align-middle">
                  <Form.Check
                    type="checkbox"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedProductIds(products.map((product) => product.id));
                      } else {
                        setSelectedProductIds([]);
                      }
                    }}
                    checked={selectedProductIds.length === products.length && products.length > 0}
                    style={{ transform: "scale(1.5)" }}
                  />
                </th>
                <th colSpan={2}>TITLE</th>
                <th>SKU</th>
                <th style={{ width: "50px", textAlign: "center" }} colSpan={2}>ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {
                products?.map((product) => (
                  <Fragment key={product.id}>
                    <ProductRow
                      product={product}
                      selectedProductIds={selectedProductIds}
                      handleCheckboxChange={handleCheckboxChange}
                      handleRemoveProduct={handleRemoveProduct}
                      expandVariants={expandVariants}
                      handleExpandVariants={handleExpandVariants} />
                    {
                      expandVariants.get(product) && product.variants.map((variant, index) => (
                        <ProductVariantRow
                          variant={variant}
                          index={index} />
                      ))
                    }
                  </Fragment>
                ))
              }
            </tbody>
          </Table>
        </Card.Body>

        <Card.Footer>
          <PaginationBar
            page={page}
            setPage={setPage}
            lastPage={totalPages}
          />
        </Card.Footer>
      </Card>

      <EditCommissionGroupDialog
        isShow={showEditGroupDialog}
        group={group!}
        onHide={(updatedGroup) => {
          setGroup(updatedGroup);
          setShowEditGroupDialog(false);
        }}
        setIsLoading={setIsLoading}
      />

      <AddProductsToCommissionGroupDialog
        isShow={showAddingProductsDialog}
        group={group!}
        setIsLoading={setIsLoading}
        onHide={() => {
          setShowAddingProductsDialog(false);
          handleGetProducts();
        }}
      />
    </Fragment>
  );
}

const ProductRow = ({
  product,
  selectedProductIds,
  handleCheckboxChange,
  handleRemoveProduct,
  expandVariants,
  handleExpandVariants
}) => {
  const getFormattedSKUs = (variants: TProductVariant[]) => {
    if (!variants) return '';
    const stringList = variants.map(variant => variant.sku).join(', ');
    return stringList.length > 50 ? `${stringList.slice(0, 50)}...` : stringList;
  }

  return (
    <tr>
      <td className="text-center align-middle">
        <Form.Check
          type="checkbox"
          onChange={() => handleCheckboxChange(product.id)}
          checked={selectedProductIds.includes(product.id)}
          style={{ transform: "scale(1.5)" }}
        />
      </td>
      <td colSpan={2}>{product.title}</td>
      <td>{expandVariants.get(product) ? ' ' : getFormattedSKUs(product.variants)}</td>
      <td style={{ textAlign: "center" }}>
        <Button
          hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION) || product.variants.length <= 1 || expandVariants.get(product)}
          variant="primary-light"
          className="btn btn-primary-light btn-sm ms-2"
          onClick={() => { handleExpandVariants(product, true) }}
        >
          <i className="bi bi-chevron-down" />
        </Button>
        <Button
          hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION) || product.variants.length <= 1 || !expandVariants.get(product)}
          variant="primary-light"
          className="btn btn-primary-light btn-sm ms-2"
          onClick={() => { handleExpandVariants(product, false) }}
        >
          <i className="bi bi-chevron-up" />
        </Button>
      </td>
      <td style={{ textAlign: "center" }}>
        <Button
          hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION)}
          variant="primary-light"
          className="btn btn-danger-light btn-sm ms-2"
          onClick={() => { handleRemoveProduct(product) }}
        >
          <span className="ri-delete-bin-7-line fs-14"></span>
        </Button>
      </td>
    </tr>
  )
}

const ProductVariantRow = ({ variant, index }) => {
  return (
    <tr key={index}>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td className="fw-light">{variant.title}</td>
      <td className="fw-light">{variant.sku}</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
  )
}

export default TierCommissionableGroup;