import { FC, Fragment, useEffect, useState } from "react";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Row } from "react-bootstrap";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import { useNavigate, useParams } from "react-router-dom";
import { useLazyGetAffiliateByIdQuery, useUpdateTierMutation } from "../../../services/affiliation/affiliate";
import { getAllErrorMessages } from "../../../utils/errors";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import RefCodeDialog from "../../../components/dialog/ref-code-dialog";
import Swal from "sweetalert2";
import AffiliateAchievedKpiCard from "./affiliate-details/affiliate-achieved-kpi-card";
import NextTierRequirementCard from "./affiliate-details/next-tier-requirements-card";
import CommissionsCard from "./affiliate-details/commissions-card";

interface AffiliateDetailProps { }

interface ErrorType {
  messages?: string[];
}

enum ETierAction {
  UPGRADE = 'upgrade',
  DOWNGRADE = 'downgrade'
}

const AffiliateDetail: FC<AffiliateDetailProps> = () => {
  const { id } = useParams();

  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<ErrorType>({});
  const [affiliate, setAffiliate] = useState<TAffiliate>();
  const [showGenerateRefCodeDialog, setShowGenerateRefCodeDialog] = useState(false);

  const navigate = useNavigate();
  const [getAffiliateById] = useLazyGetAffiliateByIdQuery();
  const [updateTier] = useUpdateTierMutation();

  useEffect(() => {
    loadData();
  }, [id]);

  const loadData = () => {
    if (!id || id === 'list-registration' || id === 'list' || id === 'verify') return;
    setIsLoading(true);

    getAffiliateById(id)
      .unwrap()
      .then((res) => {
        prepareReceivedData(res);
        setErr({});
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => setIsLoading(false));
  };

  const prepareReceivedData = (affiliate: TAffiliate) => {
    setAffiliate(affiliate);
  };

  const onFinishSetRefCode = (refCode: TAffiliateRefCode) => {
    setAffiliate({
      ...affiliate!,
      refCode: refCode
    });
  }

  const handleUpdateTier = (updateTierAction: ETierAction) => {
    if (!id) return;

    Swal.fire({
      title: "Are you sure?",
      html: `<p>Do you want to ${updateTierAction === ETierAction.UPGRADE ? 'upgrade' : 'downgrade'} tier of this affiliate?</p>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, proceed!",
    })
      .then((result) => {
        if (result.isConfirmed) {

          setIsLoading(true);
          updateTier({ affiliateId: id, tierAction: updateTierAction })
            .unwrap()
            .then(() => {
              loadData();
              setErr({});
            })
            .catch((error) => {
              setErr(getAllErrorMessages(error));
            })
            .finally(() => setIsLoading(false));
        }
      })
      .catch((error) => {
        const errorMessages = getAllErrorMessages(error);
        console.error("Error during update tier:", errorMessages);
        Swal.fire("Error!", errorMessages.messages[0], "error");
      });
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack
            title='Affiliate Details'
            route=''
          />
          <div className="px-4 justify-content-end">
            <Button
              variant="primary-light m-2"
              onClick={() => setShowGenerateRefCodeDialog(true)}
            >
              Set AFF Code
            </Button>

            <Button
              hidden={!hasPermission(ACTION.READ, RESOURCE.AFFILIATION)}
              variant="primary-light m-2"
              onClick={() => navigate("payouts")}
            >
              Payouts
            </Button>
          </div>
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}
          <Row>
            <Col xl={6}>
              <ul className="list-unstyled order-details-list">
                {[
                  [
                    'Name:',
                    `${affiliate?.user?.firstName} ${affiliate?.user?.lastName}`,
                  ],
                  [
                    'Email:',
                    `${affiliate?.user?.email}`,
                  ],
                  [
                    'Phone:',
                    `${affiliate?.user?.phone || ''}`,
                  ],
                  [
                    'Social Page(s):',
                    `${affiliate?.socialPages?.map((page) => page.link).join(', ')}`,
                  ],
                ].map((field) => (
                  <li key={Math.random()}>
                    <span className="me-2 text-default fw-semibold">
                      {field[0]}
                    </span>
                    <span className="fs-14 text-muted">
                      {field[1]}
                    </span>
                  </li>
                ))}
              </ul>
            </Col>
            <Col xl={6}>

              <ul className="list-unstyled order-details-list">
                {[
                  [
                    'Tier:',
                    `${affiliate?.affiliateTier?.title || ''}`,
                  ],
                  [
                    'AFF Code:',
                    affiliate?.refCode?.code,
                  ],
                  [
                    'Commission Percent:',
                    `${(affiliate?.affiliateTier?.defaultCommission || 0.0) * 100}%`,
                  ],
                  [
                    'Discount Percent:',
                    `${(affiliate?.affiliateTier?.defaultDiscount || 0.0) * 100}%`,
                  ]
                ].map((userDetail) => (
                  <li key={Math.random()}>
                    <span className="me-2 text-default fw-semibold">
                      {userDetail[0]}
                    </span>
                    <span className="fs-14 text-muted">
                      {userDetail[1]}
                    </span>
                  </li>
                ))}
              </ul>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      <Row>
        <Col xl={4}>
          {
            (affiliate) && <AffiliateAchievedKpiCard
              affiliateId={affiliate.id}
              cardHeight={150}
            />
          }
        </Col>

        <Col xl={4}>
          {
            (affiliate) && <NextTierRequirementCard
              currentTierId={affiliate.tierId}
              cardHeight={150}
            />
          }
        </Col>

        <Col xl={4}>
          {
            (affiliate) && <Card className="custom-card" style={{ height: '150px' }}>
              <Card.Header>
                <Card.Title>Tier Actions</Card.Title>
              </Card.Header>
              <Card.Body>

                <div className="d-flex justify-content-center gap-5">
                  <Button
                    hidden={!hasPermission(ACTION.READ, RESOURCE.AFFILIATION)}
                    variant="primary-light"
                    style={{ width: '130px' }}
                    onClick={() => { handleUpdateTier(ETierAction.DOWNGRADE) }}
                  >
                    <i className="bi bi-arrow-down-circle me-2" /> Downgrade
                  </Button>

                  <Button
                    hidden={!hasPermission(ACTION.READ, RESOURCE.AFFILIATION)}
                    variant="primary"
                    style={{ width: '130px' }}
                    onClick={() => { handleUpdateTier(ETierAction.UPGRADE) }}
                  >
                    <i className="bi bi-arrow-up-circle-fill me-2" /> Upgrade
                  </Button>
                </div>

              </Card.Body>
            </Card>
          }
        </Col>
      </Row>

      {
        (affiliate) && <CommissionsCard affiliateId={affiliate.id} />
      }

      <RefCodeDialog
        show={showGenerateRefCodeDialog}
        setShow={setShowGenerateRefCodeDialog}
        setIsLoading={setIsLoading}
        affiliate={affiliate}
        onFinishedSetRefCode={onFinishSetRefCode}
      />

    </Fragment>
  );
}

export default AffiliateDetail;