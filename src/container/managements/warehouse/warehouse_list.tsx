import debounce from "lodash/debounce";
import { FC, Fragment, useCallback, useEffect, useState } from "react";
import {
  Button,
  Card,
  Col,
  Form,
  OverlayTrigger,
  Row,
  Table,
  Tooltip
} from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import deleteSweetAlert from "../../../components/sweet-alerts/delete_swal";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import { useLazyDeleteWarehouseByIdQuery, useLazyListWarehouseQuery } from "../../../services/warehouse";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
interface ManagementWarehouseProps { }

const ManagementWarehouse: FC<
  ManagementWarehouseProps
> = () => {
  const [page, setPage] = useState(1);
  // const [limit] = useState(10);
  const [totalPages, setTotalPages] = useState(20);
  const [total, setTotal] = useState(20)
  const [search, setSearch] = useState<string>("");

  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false)
  // const [err, setErr] = useState<any>({});

  const [trigger] = useLazyListWarehouseQuery();
  const [deleteById] = useLazyDeleteWarehouseByIdQuery();

  const navigate = useNavigate();
  const [warehouses, setWarehouses] = useState<TWarehouse[]>([]);

  const loadData = (query) => {
    setIsLoading(true);
    trigger({
      ...query,
    })
      .then((res) => {
        setWarehouses(res.data?.data || [])
        setTotalPages(res?.data?.meta?.lastPage);
        setTotal(res.data?.meta.total || 0)
      })
      .catch((error) => console.log(error))
      .finally(() => {
        setIsLoading(false);
        setIsSearching(false)
      });
  };

  const debouncedHandleSearch = useCallback(
    debounce((query) => {
      setPage(query.page)
      setSearch(query.search)

      loadData(query)
    }, 1000), [])

  useEffect(() => {
    if (isSearching) { return }
    loadData({
      page, search,
    })
  }, [page])

  const handleSearch = (event) => {
    if (event) { event.preventDefault() }

    setIsSearching(true)
    debouncedHandleSearch({
      search: event.target.value,
      page: 1,
    })
  }

  const handleDeleteClick = (categoryId: string) => {
    deleteSweetAlert({
      id: categoryId,
      deleteAction: deleteById,
      prepareAction: () => setIsLoading(true),
      finishAction: () =>
        setWarehouses((prevCategories) =>
          prevCategories.filter((category) => category.id !== categoryId)
        ),
      finalAction: () => setIsLoading(false),
    });
  };

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Marketplace Category Management"
                route=""
              ></CardHeaderWithBack>
              <div className="px-4 justify-content-end">
                <Button
                  hidden={
                    !hasPermission(ACTION.CREATE, RESOURCE.POST_CATEGORY)
                  }
                  variant="primary-light"
                  type="submit"
                  onClick={() => navigate("new")}
                >
                  Add<i className="bi bi-plus-lg ms-2"></i>
                </Button>
              </div>
            </Card.Header>
            <Card.Body className="overflow-auto">
              <div className="app-container">
                <div className="mb-3">
                  <Form.Group className="text-center">
                    <Form.Control
                      type="search"
                      onChange={handleSearch}
                    />
                  </Form.Group>
                </div>
                <Table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Code</th>
                      <th>Inventories</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {warehouses.map((warehouse) => (
                      <Fragment key={warehouse.id}>
                        <ReadOnlyRow
                          // err={err}
                          // setErr={setErr}
                          warehouse={warehouse}
                          handleDeleteClick={handleDeleteClick}
                        />
                      </Fragment>
                    ))}
                  </tbody>
                </Table>
                <PaginationBar
                  page={page}
                  setPage={setPage}
                  lastPage={totalPages}
                  total={total}
                />
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

const ReadOnlyRow = ({ warehouse, handleDeleteClick }: any) => {
  const navigate = useNavigate();

  return (
    <tr>
      <td
        style={{
          maxWidth: "150px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
      >
        <span onClick={() => { navigate(warehouse.id); }} style={{ cursor: "pointer" }}>
          {warehouse.name}
        </span>
        {
          warehouse.isVendorPrimary &&
          <Fragment>
            {" "}
            <OverlayTrigger overlay={<Tooltip>Main Warehouse for Vendors</Tooltip>}>
              <i className="bx bx-store-alt text-primary" />
            </OverlayTrigger>
          </Fragment>
        }
      </td>
      <td>{warehouse.code}</td>
      <td>{warehouse.inventoriesCount}</td>
      <td>
        <OverlayTrigger placement="top" overlay={<Tooltip>Edit</Tooltip>}>
          <Button
            variant="primary-light"
            className="btn btn-warning-light btn-sm ms-2"
            hidden={!hasPermission(ACTION.UPDATE, RESOURCE.POST_CATEGORY)}
            onClick={() => { navigate(warehouse.id) }}
          >
            <span className="ri-edit-line fs-14"></span>
          </Button>
        </OverlayTrigger>

        <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
          <Button
            variant="primary-light"
            className="btn btn-danger-light btn-sm ms-2"
            hidden={!hasPermission(ACTION.DELETE, RESOURCE.POST_CATEGORY)}
            onClick={() => handleDeleteClick(warehouse.id)}
          >
            <span className="ri-delete-bin-7-line fs-14"></span>
          </Button>
        </OverlayTrigger>
      </td>
    </tr>
  );
};

export default ManagementWarehouse;
