import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Form, Row } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
//filepond
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import deleteSweetAlert from "../../../components/sweet-alerts/delete_swal";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import {
  useCreateWarehouseMutation,
  useLazyDeleteWarehouseByIdQuery,
  useLazyGetDataToCreateWarehouseQuery,
  useLazyGetDataToUpdateWarehouseQuery,
  useUpdateWarehouseMutation
} from "../../../services/warehouse";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { getAllErrorMessages } from "../../../utils/errors";
import { ManagementWarehouseInventoryList } from "./warehouse_intentory_list";

interface ManagementWarehouseDetailsProps { }

const ManagementWarehouseDetails: FC<
  ManagementWarehouseDetailsProps
> = () => {
  const { id, edit } = useParams();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>();

  const [getDataToCreate] = useLazyGetDataToCreateWarehouseQuery();
  const [getDataToUpdate] = useLazyGetDataToUpdateWarehouseQuery();
  const [createWarehouse] = useCreateWarehouseMutation();
  // const [getPostCategoryById] = useLazyGetPostCategoryByIdQuery();
  const [updateWarehouse] = useUpdateWarehouseMutation();
  const [deleteWarehouse] = useLazyDeleteWarehouseByIdQuery();

  const navigate = useNavigate();
  const listPage = '/managements-warehouses'

  const [detailsFormData, setDetailsFormData] = useState<any>({});

  useEffect(() => {
    setIsAdd(false);
    setIsEdit(false);

    if (id == 'new') {
      setIsAdd(true)
    } else {
      setIsEdit(true);
    }
  }, [id, edit]);

  useEffect(() => {
    switch (true) {
      case isAdd: {
        setIsLoading(true);
        getDataToCreate({})
          .unwrap()
          .then((res) => {
            res;
          })
          .catch((error) => {
            setErr(getAllErrorMessages(error));
          })
          .finally(() => {
            setIsLoading(false);
          });
        break;
      }
      case isEdit: {
        setIsLoading(true);
        getDataToUpdate(id || "")
          .unwrap()
          .then((res) => {
            const details: any = {
              ...res.data
            };

            setDetailsFormData(details);
          })
          .catch((error) => {
            setErr(getAllErrorMessages(error));
          })
          .finally(() => {
            setIsLoading(false);
          });
        break;
      }
      default:
        break;
    }
  }, [isAdd, isEdit]); // Empty dependency array ensures this effect runs only once on mount

  const handleDetailsFormChange = (event: any) => {
    if (event) { event.preventDefault(); }

    const fieldName = event.target.getAttribute("name");
    const fieldValue = event.target.value;

    const newFormData: any = { ...detailsFormData };
    newFormData[fieldName] = fieldValue;

    setDetailsFormData(newFormData);

    if (err?.validationErrors) {
      if (err.validationErrors[fieldName]) {
        delete err.validationErrors[fieldName];
      }
    }
  };

  const handCheckFormChange = (event: any) => {
    const fieldName = event.target.getAttribute("name");
    const fieldValue = event.target.checked ? 1 : 0;

    const newFormData: any = { ...detailsFormData };
    newFormData[fieldName] = fieldValue;

    setDetailsFormData(newFormData);
  };

  const prepareFormSubmit = () => {
    const preparedForm = {
      ...detailsFormData
    }

    return preparedForm
  }

  const handleAddFormSubmit = (event: any) => {
    if (event) { event.preventDefault(); }

    const newWarehouse = prepareFormSubmit()

    setIsLoading(true);
    createWarehouse({ ...newWarehouse })
      .unwrap()
      .then(() => {
        setDetailsFormData({});
        navigate(listPage);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleUpdateFormSubmit = (event: any) => {
    if (event) {
      event.preventDefault();
    }

    const updatedWarehouse = prepareFormSubmit()

    setIsLoading(true);
    updateWarehouse({ id: detailsFormData.categoryId, ...updatedWarehouse })
      .unwrap()
      .then(() => {
        setDetailsFormData({});
        navigate(listPage);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleDeleteClick = () => {
    deleteSweetAlert({
      id: id || "",
      deleteAction: deleteWarehouse,
      prepareAction: (() => setIsLoading(true)),
      finishAction: (() => navigate(listPage)),
      finalAction: (() => setIsLoading(false)),
    })
  };

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack
            title="Warehouse Details"
            route={listPage}
          ></CardHeaderWithBack>
        </Card.Header>
        <div>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}
        </div>
        <Card.Body>
          <Col className="p-1">
            <Form.Label>ID</Form.Label>
            <Form.Control
              required
              type="numeric"
              name="fulfilWarehouseId"
              placeholder="fulfilWarehouseId"
              value={detailsFormData?.fulfilWarehouseId || ""}
              onChange={handleDetailsFormChange}
              isInvalid={err?.validationErrors?.fulfilWarehouseId}
            />
            <Form.Control.Feedback className="invalid-feedback">
              {err?.validationErrors?.fulfilWarehouseId}
            </Form.Control.Feedback>
          </Col>
          <Col className="p-1">
            <Form.Label>Name</Form.Label>
            <Form.Control
              required
              isInvalid={err?.validationErrors?.name}
              type="text"
              name="name"
              placeholder="name"
              value={detailsFormData?.name || ""}
              onChange={handleDetailsFormChange}
            />
            <Form.Control.Feedback className="invalid-feedback">
              {err?.validationErrors?.name}
            </Form.Control.Feedback>
          </Col>
          <Col className="p-1">
            <Form.Label>Code</Form.Label>
            <Form.Control
              required
              isInvalid={err?.validationErrors?.code}
              type="text"
              name="code"
              placeholder="code"
              value={detailsFormData?.code || ""}
              onChange={handleDetailsFormChange}
            />
            <Form.Control.Feedback className="invalid-feedback">
              {err?.validationErrors?.code}
            </Form.Control.Feedback>
          </Col>
          <Row>
            <Col className="p-1">
              <Form.Check
                type="checkbox"
                name="isNotification"
                label="isNotification"
                checked={detailsFormData?.isNotification ? true : false}
                onChange={handCheckFormChange}
              />
            </Col>

            {
              detailsFormData.shopifyLocationId &&
              <Col className="p-1">
                <Form.Check
                  type="checkbox"
                  name="isVendorPrimary"
                  label="isVendorPrimary"
                  checked={detailsFormData?.isVendorPrimary ? true : false}
                  onChange={handCheckFormChange}
                />
              </Col>
            }
          </Row>
        </Card.Body>

      </Card>
      {
        (id && id != 'new') &&
        <ManagementWarehouseInventoryList
          warehouseId={(id && id != 'new') ? id : ""}
        />
      }
      <Card>
        <div className="px-4 py-3 border-top border-block-start-dashed d-sm-flex justify-content-end">
          {isAdd ? (
            <Button
              hidden={!hasPermission(ACTION.CREATE, RESOURCE.POST_CATEGORY)}
              variant="primary-light"
              onClick={handleAddFormSubmit}
            >
              Add<i className="bi bi-plus-lg ms-2"></i>
            </Button>
          ) : isEdit
            ? (
              <div>
                <Button
                  hidden={!hasPermission(ACTION.DELETE, RESOURCE.POST_CATEGORY)}
                  className="mx-2"
                  variant="danger-light"
                  onClick={handleDeleteClick}
                >
                  Delete<i className="bi bi-trash ms-2"></i>
                </Button>

                <Button
                  hidden={
                    !hasPermission(ACTION.UPDATE, RESOURCE.POST_CATEGORY)
                  }
                  variant="success-light"
                  onClick={handleUpdateFormSubmit}
                >
                  Save <i className="bi bi-download ms-2"></i>
                </Button>
              </div>
            ) : null}
        </div>
      </Card>
    </Fragment>
  );
};

export default ManagementWarehouseDetails;
