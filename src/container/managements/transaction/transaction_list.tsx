import React, { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Form, Modal, Row, Table } from "react-bootstrap";
import { Link } from "react-router-dom";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import { useLazyListTransactionsQuery, useLazyGetTransactionStatsQuery } from "../../../services/transaction";
import { debounce } from "lodash";
import moment from "moment";
import { currencySymbol } from "../../../utils/constant/currency";

interface ManagementTransactionProps { }

const ManagementTransaction: FC<ManagementTransactionProps> = () => {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [lastPage, setLastPage] = useState(20);
  const [total, setTotal] = useState(20);
  const [search, setSearch] = useState("");
  const [source, setSource] = useState<string>("");
  const [status, setStatus] = useState<string>("");
  const setDebouncedSearch = debounce((value) => setSearch(value), 1000);

  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState<TTransactionStats | null>(null);

  const [trigger] = useLazyListTransactionsQuery();
  const [getStats] = useLazyGetTransactionStatsQuery();

  const [transactions, setTransactions] = useState<TTransaction[]>([]);
  const [selectedTransaction, setSelectedTransaction] = useState<TTransaction | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  useEffect(() => {
    setIsLoading(true);
    trigger({ page, limit, search, source: source || undefined, status: status || undefined })
      .unwrap()
      .then((res) => {
        setTransactions(res.data || []);
        setLastPage(res?.meta?.lastPage);
        setTotal(res?.meta?.total);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [page, limit, search, source, status]);

  useEffect(() => {
    getStats()
      .unwrap()
      .then((res) => {
        setStats(res);
      });
  }, []);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      COMPLETED: { variant: "success", text: "Completed" },
      REFUNDED: { variant: "warning", text: "Refunded" },
      CAPTURED: { variant: "info", text: "Captured" },
      VOIDED: { variant: "danger", text: "Voided" },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { variant: "secondary", text: status };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const getSourceBadge = (source: string) => {
    const sourceConfig = {
      PAYPAL: { variant: "primary", text: "PayPal" },
      AUTHORIZE_NET: { variant: "info", text: "Authorize.net" },
      CASH: { variant: "success", text: "Cash" },
      OTHER: { variant: "secondary", text: "Other" },
    };
    
    const config = sourceConfig[source as keyof typeof sourceConfig] || { variant: "secondary", text: source };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const handleViewDetails = (transaction: TTransaction) => {
    setSelectedTransaction(transaction);
    setShowDetailsModal(true);
  };

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Transaction Management"
                route=""
              ></CardHeaderWithBack>
            </Card.Header>
            <Card.Body className="overflow-auto">
              {/* Stats Cards */}
              {stats && (
                <Row className="mb-4">
                  <Col md={2}>
                    <Card className="text-center">
                      <Card.Body>
                        <h4 className="text-primary">{stats.total}</h4>
                        <small>Total Transactions</small>
                      </Card.Body>
                    </Card>
                  </Col>
                  <Col md={2}>
                    <Card className="text-center">
                      <Card.Body>
                        <h4 className="text-success">{stats.completed}</h4>
                        <small>Completed</small>
                      </Card.Body>
                    </Card>
                  </Col>
                  <Col md={2}>
                    <Card className="text-center">
                      <Card.Body>
                        <h4 className="text-warning">{stats.refunded}</h4>
                        <small>Refunded</small>
                      </Card.Body>
                    </Card>
                  </Col>
                  <Col md={2}>
                    <Card className="text-center">
                      <Card.Body>
                        <h4 className="text-info">{stats.captured}</h4>
                        <small>Captured</small>
                      </Card.Body>
                    </Card>
                  </Col>
                  <Col md={2}>
                    <Card className="text-center">
                      <Card.Body>
                        <h4 className="text-danger">{stats.voided}</h4>
                        <small>Voided</small>
                      </Card.Body>
                    </Card>
                  </Col>
                </Row>
              )}

              <div className="app-container">
                <Row className="mb-3">
                  <Col md={4}>
                    <Form.Group>
                      <Form.Control
                        type="search"
                        placeholder="Search by transaction ID, order ID, or payer email..."
                        onChange={(e) => setDebouncedSearch(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <Form.Select
                      value={source}
                      onChange={(e) => setSource(e.target.value)}
                    >
                      <option value="">All Sources</option>
                      <option value="PAYPAL">PayPal</option>
                      <option value="AUTHORIZE_NET">Authorize.net</option>
                      <option value="CASH">Cash</option>
                      <option value="OTHER">Other</option>
                    </Form.Select>
                  </Col>
                  <Col md={3}>
                    <Form.Select
                      value={status}
                      onChange={(e) => setStatus(e.target.value)}
                    >
                      <option value="">All Statuses</option>
                      <option value="COMPLETED">Completed</option>
                      <option value="REFUNDED">Refunded</option>
                      <option value="CAPTURED">Captured</option>
                      <option value="VOIDED">Voided</option>
                    </Form.Select>
                  </Col>
                </Row>

                <Table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th className="text-center">Transaction ID</th>
                      <th className="text-center">Order</th>
                      <th className="text-center">Amount</th>
                      <th className="text-center">Source</th>
                      <th className="text-center">Status</th>
                      <th className="text-center">Payer</th>
                      <th className="text-center">Date</th>
                      <th className="text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {transactions.map((transaction: TTransaction) => (
                      <tr key={transaction.id}>
                        <td className="text-center">
                          <small className="text-muted">{transaction.transactionId}</small>
                        </td>
                        <td className="text-center">
                          <Link to={`/managements-orders/details/${transaction.orderId}`}>
                            {transaction.order?.name || transaction.orderId}
                          </Link>
                        </td>
                        <td className="text-center">
                          <strong>
                            {currencySymbol(transaction.currency)}{transaction.amount.toFixed(2)}
                          </strong>
                          {transaction.refundAmount && (
                            <div>
                              <small className="text-danger">
                                Refunded: {currencySymbol(transaction.currency)}{transaction.refundAmount.toFixed(2)}
                              </small>
                            </div>
                          )}
                        </td>
                        <td className="text-center">
                          {getSourceBadge(transaction.source)}
                        </td>
                        <td className="text-center">
                          {getStatusBadge(transaction.status)}
                        </td>
                        <td className="text-center">
                          <div>
                            <div>{transaction.payerName}</div>
                            <small className="text-muted">{transaction.payerEmail}</small>
                          </div>
                        </td>
                        <td className="text-center">
                          <small>{moment(transaction.processedAt).format('MMM DD, YYYY HH:mm')}</small>
                        </td>
                        <td className="text-center">
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => handleViewDetails(transaction)}
                          >
                            View Details
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>

                <PaginationBar
                  currentPage={page}
                  lastPage={lastPage}
                  total={total}
                  onPageChange={setPage}
                />
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Transaction Details Modal */}
      <Modal
        show={showDetailsModal}
        onHide={() => setShowDetailsModal(false)}
        size="lg"
      >
        <Modal.Header closeButton>
          <Modal.Title>Transaction Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedTransaction && (
            <Row>
              <Col md={6}>
                <h6>Transaction Information</h6>
                <p><strong>Transaction ID:</strong> {selectedTransaction.transactionId}</p>
                <p><strong>Authorization ID:</strong> {selectedTransaction.authorizationId}</p>
                <p><strong>Amount:</strong> {currencySymbol(selectedTransaction.currency)}{selectedTransaction.amount.toFixed(2)}</p>
                <p><strong>Source:</strong> {getSourceBadge(selectedTransaction.source)}</p>
                <p><strong>Status:</strong> {getStatusBadge(selectedTransaction.status)}</p>
                <p><strong>Processed:</strong> {moment(selectedTransaction.processedAt).format('MMM DD, YYYY HH:mm')}</p>
                {selectedTransaction.refundedAt && (
                  <p><strong>Refunded:</strong> {moment(selectedTransaction.refundedAt).format('MMM DD, YYYY HH:mm')}</p>
                )}
              </Col>
              <Col md={6}>
                <h6>Payer Information</h6>
                <p><strong>Name:</strong> {selectedTransaction.payerName}</p>
                <p><strong>Email:</strong> {selectedTransaction.payerEmail}</p>
                
                <h6 className="mt-3">Order Information</h6>
                <p><strong>Order ID:</strong> {selectedTransaction.orderId}</p>
                <p><strong>Order Name:</strong> {selectedTransaction.order?.name}</p>
                
                {selectedTransaction.refundAmount && (
                  <div className="mt-3">
                    <h6>Refund Information</h6>
                    <p><strong>Refund Amount:</strong> {currencySymbol(selectedTransaction.currency)}{selectedTransaction.refundAmount.toFixed(2)}</p>
                    {selectedTransaction.refundReason && (
                      <p><strong>Reason:</strong> {selectedTransaction.refundReason}</p>
                    )}
                  </div>
                )}
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDetailsModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </Fragment>
  );
};

export default ManagementTransaction; 