import React, { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>ton, Card, Col, Form, Modal, Row, Table } from "react-bootstrap";
import { useParams, useNavigate } from "react-router-dom";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import { useLazyGetTransactionByIdQuery, useRefundTransactionMutation } from "../../../../services/transaction";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import moment from "moment";
import { currencySymbol } from "../../../../utils/constant/currency";
import { toast } from "react-toastify";

interface ManagementTransactionDetailsProps { }

const ManagementTransactionDetails: FC<ManagementTransactionDetailsProps> = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [isLoading, setIsLoading] = useState(false);
  const [transaction, setTransaction] = useState<TTransaction | null>(null);
  const [showRefundModal, setShowRefundModal] = useState(false);
  const [refundAmount, setRefundAmount] = useState<number>(0);
  const [refundReason, setRefundReason] = useState<string>("");
  const [isRefunding, setIsRefunding] = useState(false);

  const [getTransaction] = useLazyGetTransactionByIdQuery();
  const [refundTransaction] = useRefundTransactionMutation();

  useEffect(() => {
    if (id) {
      loadTransaction();
    }
  }, [id]);

  const loadTransaction = async () => {
    if (!id) return;

    setIsLoading(true);
    try {
      const result = await getTransaction(id).unwrap();
      setTransaction(result);
      setRefundAmount(result.amount);
    } catch (error: any) {
      const errorMessage = error?.data?.message || error?.message || "Failed to load transaction details";
      toast.error(errorMessage);
      navigate("/managements-transactions");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefund = async () => {
    if (!id || !transaction) return;

    // Validation
    if (refundAmount <= 0) {
      toast.error("Refund amount must be greater than 0");
      return;
    }

    if (refundAmount > transaction.amount) {
      toast.error("Refund amount cannot exceed transaction amount");
      return;
    }

    if (!refundReason.trim()) {
      toast.error("Please provide a reason for the refund");
      return;
    }

    setIsRefunding(true);
    try {
      await refundTransaction({
        id,
        data: {
          amount: refundAmount,
          reason: refundReason,
        },
      }).unwrap();

      toast.success("Transaction refunded successfully");
      setShowRefundModal(false);
      setRefundReason("");
      loadTransaction(); // Reload to get updated data
    } catch (error: any) {
      const errorMessage = error?.data?.message || error?.message || "Failed to refund transaction";
      toast.error(errorMessage);
    } finally {
      setIsRefunding(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      COMPLETED: { variant: "success", text: "Completed" },
      REFUNDED: { variant: "warning", text: "Refunded" },
      CAPTURED: { variant: "info", text: "Captured" },
      VOIDED: { variant: "danger", text: "Voided" },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { variant: "secondary", text: status };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const getSourceBadge = (source: string) => {
    const sourceConfig = {
      PAYPAL: { variant: "primary", text: "PayPal" },
      AUTHORIZE_NET: { variant: "info", text: "Authorize.net" },
      CASH: { variant: "success", text: "Cash" },
      OTHER: { variant: "secondary", text: "Other" },
    };

    const config = sourceConfig[source as keyof typeof sourceConfig] || { variant: "secondary", text: source };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const handleOpenRefundModal = () => {
    if (transaction) {
      setRefundAmount(transaction.amount);
      setRefundReason("");
      setShowRefundModal(true);
    }
  };

  const handleCloseRefundModal = () => {
    setShowRefundModal(false);
    setRefundReason("");
    setRefundAmount(transaction?.amount || 0);
  };

  const formatCurrency = (amount: number, currency: string) => {
    const symbol = currencySymbol[currency.toLowerCase() as keyof typeof currencySymbol] || currency;
    return `${symbol}${amount.toFixed(2)}`;
  };

  if (!transaction) {
    return <LoadingOverlay />;
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Transaction Details"
                route="/managements-transactions"
              ></CardHeaderWithBack>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col lg={8}>
                  {/* Transaction Information */}
                  <Card className="custom-card">
                    <Card.Header>
                      <Card.Title>Transaction Information</Card.Title>
                    </Card.Header>
                    <Card.Body>
                      <Row>
                        <Col md={6}>
                          <p><strong>Transaction ID:</strong></p>
                          <p className="text-muted">{transaction.transactionId}</p>
                        </Col>
                        <Col md={6}>
                          <p><strong>Authorization ID:</strong></p>
                          <p className="text-muted">{transaction.authorizationId}</p>
                        </Col>
                      </Row>
                      <Row>
                        <Col md={6}>
                          <p><strong>Amount:</strong></p>
                          <p className="text-primary fs-5">
                            {formatCurrency(transaction.amount, transaction.currency)}
                          </p>
                        </Col>
                        <Col md={6}>
                          <p><strong>Currency:</strong></p>
                          <p className="text-muted">{transaction.currency}</p>
                        </Col>
                      </Row>
                      <Row>
                        <Col md={6}>
                          <p><strong>Source:</strong></p>
                          <p>{getSourceBadge(transaction.source)}</p>
                        </Col>
                        <Col md={6}>
                          <p><strong>Status:</strong></p>
                          <p>{getStatusBadge(transaction.status)}</p>
                        </Col>
                      </Row>
                      <Row>
                        <Col md={6}>
                          <p><strong>Processed Date:</strong></p>
                          <p className="text-muted">
                            {moment(transaction.processedAt).format('MMM DD, YYYY HH:mm:ss')}
                          </p>
                        </Col>
                        {transaction.refundedAt && (
                          <Col md={6}>
                            <p><strong>Refunded Date:</strong></p>
                            <p className="text-muted">
                              {moment(transaction.refundedAt).format('MMM DD, YYYY HH:mm:ss')}
                            </p>
                          </Col>
                        )}
                      </Row>
                    </Card.Body>
                  </Card>

                  {/* Payer Information */}
                  <Card className="custom-card mt-3">
                    <Card.Header>
                      <Card.Title>Payer Information</Card.Title>
                    </Card.Header>
                    <Card.Body>
                      <Row>
                        <Col md={6}>
                          <p><strong>Name:</strong></p>
                          <p className="text-muted">{transaction.payerName}</p>
                        </Col>
                        <Col md={6}>
                          <p><strong>Email:</strong></p>
                          <p className="text-muted">{transaction.payerEmail}</p>
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>

                  {/* Order Information */}
                  <Card className="custom-card mt-3">
                    <Card.Header>
                      <Card.Title>Order Information</Card.Title>
                    </Card.Header>
                    <Card.Body>
                      <Row>
                        <Col md={6}>
                          <p><strong>Order ID:</strong></p>
                          <p className="text-muted">{transaction.orderId}</p>
                        </Col>
                        <Col md={6}>
                          <p><strong>Order Name:</strong></p>
                          <p className="text-muted">{transaction.order?.name || 'N/A'}</p>
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>

                  {/* Refund Information */}
                  {transaction.refundAmount && (
                    <Card className="custom-card mt-3">
                      <Card.Header>
                        <Card.Title>Refund Information</Card.Title>
                      </Card.Header>
                      <Card.Body>
                        <Row>
                          <Col md={6}>
                            <p><strong>Refund Amount:</strong></p>
                            <p className="text-danger fs-5">
                              {formatCurrency(transaction.refundAmount, transaction.currency)}
                            </p>
                          </Col>
                          <Col md={6}>
                            <p><strong>Refund Reason:</strong></p>
                            <p className="text-muted">{transaction.refundReason || 'N/A'}</p>
                          </Col>
                        </Row>
                      </Card.Body>
                    </Card>
                  )}
                </Col>

                <Col lg={4}>
                  {/* Actions */}
                  <Card className="custom-card">
                    <Card.Header>
                      <Card.Title>Actions</Card.Title>
                    </Card.Header>
                    <Card.Body>
                      {transaction.status === 'COMPLETED' &&
                       !transaction.refundAmount &&
                       hasPermission(RESOURCE.TRANSACTION, ACTION.UPDATE) && (
                        <Button
                          variant="warning"
                          className="w-100 mb-2"
                          onClick={handleOpenRefundModal}
                        >
                          Process Refund
                        </Button>
                      )}

                      {transaction.status === 'REFUNDED' && (
                        <div className="alert alert-warning text-center">
                          <small>This transaction has been refunded</small>
                        </div>
                      )}
                      
                      <Button
                        variant="outline-secondary"
                        className="w-100"
                        onClick={() => navigate("/managements-transactions")}
                      >
                        Back to Transactions
                      </Button>
                    </Card.Body>
                  </Card>

                  {/* Metadata */}
                  {transaction.metadata && Object.keys(transaction.metadata).length > 0 && (
                    <Card className="custom-card mt-3">
                      <Card.Header>
                        <Card.Title>Additional Data</Card.Title>
                      </Card.Header>
                      <Card.Body>
                        <Table className="table table-sm">
                          <tbody>
                            {Object.entries(transaction.metadata).map(([key, value]) => (
                              <tr key={key}>
                                <td><strong>{key}:</strong></td>
                                <td>{String(value)}</td>
                              </tr>
                            ))}
                          </tbody>
                        </Table>
                      </Card.Body>
                    </Card>
                  )}
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Refund Modal */}
      <Modal
        show={showRefundModal}
        onHide={handleCloseRefundModal}
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>Process Refund</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {transaction && (
            <Form>
              <Form.Group className="mb-3">
                <Form.Label>Refund Amount</Form.Label>
                <Form.Control
                  type="number"
                  step="0.01"
                  min="0"
                  max={transaction.amount}
                  value={refundAmount}
                  onChange={(e) => setRefundAmount(parseFloat(e.target.value) || 0)}
                  disabled={isRefunding}
                  required
                />
                <Form.Text className="text-muted">
                  Maximum refund amount: {formatCurrency(transaction.amount, transaction.currency)}
                </Form.Text>
              </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Refund Reason</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={refundReason}
                onChange={(e) => setRefundReason(e.target.value)}
                placeholder="Enter reason for refund..."
                required
                disabled={isRefunding}
              />
            </Form.Group>
          </Form>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="secondary"
            onClick={handleCloseRefundModal}
            disabled={isRefunding}
          >
            Cancel
          </Button>
          <Button
            variant="warning"
            onClick={handleRefund}
            disabled={isRefunding || refundAmount <= 0 || !refundReason.trim()}
          >
            {isRefunding ? "Processing..." : "Process Refund"}
          </Button>
        </Modal.Footer>
      </Modal>
    </Fragment>
  );
};

export default ManagementTransactionDetails; 