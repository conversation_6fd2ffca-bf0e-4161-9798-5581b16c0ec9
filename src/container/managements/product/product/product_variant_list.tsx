import React, { FC, Fragment, useState } from "react";
import { <PERSON><PERSON>, Card, Col, Row, Table, } from "react-bootstrap";
// import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import { useNavigate } from "react-router-dom";
import deleteSweetAlert from "../../../../components/sweet-alerts/delete_swal";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import SearchBar from "../../../../components/search-bar/search-bar";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import { useDeleteProductVariantByIdMutation, useLazyListProductVariantQuery } from "../../../../services/product-variant";

interface ManagementProductVariantListProps {
  productId: string
}

export const ManagementProductVariantList: FC<ManagementProductVariantListProps> = ({
  productId
}) => {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [totalPages, setTotalPages] = useState(20);
  // const [search, setSearch] = useState<string>("");

  // const [isInitial, setIsInitial] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});

  const [trigger] = useLazyListProductVariantQuery();
  const [deleteById] = useDeleteProductVariantByIdMutation();

  const [productVariants, setProductVariants] = useState<TProductVariant[]>([]);

  const navigate = useNavigate();

  // const [searchFieldOptions] = useState([
  //   { label: "Username", value: "email" },
  //   { label: "First Name", value: "firstName" },
  //   { label: "Last Name", value: "lastName" },
  // ])

  // const [sortOptions] = useState([
  //   {
  //     label: "Newest",
  //     value: {
  //       field: "createdAt",
  //       direction: "desc",
  //     }
  //   },
  //   {
  //     label: "Oldest",
  //     value: {
  //       field: "createdAt",
  //       direction: "asc",
  //     }
  //   },
  // ])

  // const [filterOptions] = useState([
  //   {
  //     field: {
  //       label: "Status",
  //       value: "status"
  //     },
  //     options: [
  //       { label: "Active", value: 'active' },
  //       { label: "Draft", value: 'draft' },
  //       { label: "Archived", value: 'archived' },
  //     ]
  //   }
  // ])

  const handleDeleteClick = (productVariant: TProductVariant) => {
    deleteSweetAlert({
      id: productVariant.id,
      deleteAction: deleteById,
      confirmText: `Do you want to delete ${productVariant.title} ?`,
      prepareAction: (() => setIsLoading(true)),
      // @ts-ignore
      finishAction: (() => trigger({ page, limit, productId }).then((res) => {
        setProductVariants(res.data?.data || []);
      })),
      finalAction: (() => setIsLoading(false)),
    })
  };

  return (
    <Card>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <div className="d-flex align-items-center">
                <span
                  className="mb-0 ms-2"
                  style={{ fontWeight: "bold", fontSize: "1.2em" }}
                >
                  Variant Managements
                </span>
              </div>
              <div className="justify-content-end">
                <Button
                  disabled
                  hidden={!hasPermission(ACTION.CREATE, RESOURCE.PRODUCT)}
                  variant="primary-light"
                  onClick={() => navigate("/managements-products/details/")}
                >
                  Add<i className="bi bi-plus-lg ms-2"></i>
                </Button>
              </div>
            </Card.Header>
            <Card.Body className="overflow-auto">
              <div className="app-container">
                <div className="mb-3">
                  {productId &&
                    <SearchBar
                      loadFunction={trigger}
                      loadFunctionParams={{
                        productId: productId
                      }}
                      page={page}
                      getIsLoading={setIsLoading}
                      getPagination={(pagination) => {
                        setProductVariants(pagination.data)
                        setTotalPages(pagination.meta.lastPage)
                      }}
                    // searchFieldOptions={searchFieldOptions}
                    // sortOptions={sortOptions}
                    // filterOptions={filterOptions}
                    />
                  }
                  {/* <Form.Group className="text-center">
                    <Form.Control
                      key="search"
                      type="text"
                      name="Search"
                      value={search}
                      // onChange={handleInputSearch}
                      placeholder="Search..."
                    />
                  </Form.Group> */}
                </div>
                <Table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th>Image</th>
                      <th>Title</th>
                      <th>SKU</th>
                      <th>Price</th>
                      <th>Quantity</th>
                      <th>Inventories</th>
                      {/* <th>Actions</th> */}
                    </tr>
                  </thead>
                  <tbody>
                    {productVariants.map((productVariant: TProductVariant) => (
                      <Fragment key={productVariant.id}>
                        <ReadOnlyRow
                          productVariant={productVariant}
                          handleDeleteClick={handleDeleteClick}
                          err={err}
                          setErr={setErr}
                        />
                      </Fragment>
                    ))}
                  </tbody>
                </Table>
                <PaginationBar
                  page={page}
                  setPage={setPage}
                  lastPage={totalPages}
                />
              </div>
            </Card.Body>
            <Card.Footer>

            </Card.Footer>
          </Card>
        </Col>
      </Row>
    </Card>
  );
};

const ReadOnlyRow = ({
  productVariant: productVariant,
  // handleDeleteClick,
}: // err,
  // setErr,
  any) => {
  // const navigate = useNavigate();

  return (
    <tr>
      <td
        style={{
          textAlign: "center",
          padding: "10px",
          width: "100px",
          height: "100px",
          overflow: "hidden",
        }}>
        <p className="avatar avatar-xxl bg-dark-transparent my-auto">
          <img
            src={productVariant.image?.src || ""}
            alt="img"
            style={{
              display: "block",
              margin: "0 auto",
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
          />
        </p>
      </td>
      <td>{productVariant.title}</td>
      <td>{productVariant.sku}</td>
      <td>{productVariant.price}</td>
      <td>{productVariant.inventoryQuantity}</td>
      <td>
        <table>
          <thead>
            <tr>
              <th>Warehouse</th>
              <th>Available</th>
              <th>On Hand</th>
            </tr>
          </thead>
          <tbody>
            {
              productVariant.inventories?.map((inventory, index) => (
                <Fragment key={index}>
                  <tr>
                    <td>{inventory.warehouse?.name}</td>
                    <td>{inventory.quantityAvailable}</td>
                    <td>{inventory.quantityOnHand}</td>
                  </tr>
                </Fragment>
              ))
            }
          </tbody>
        </table>
      </td>
      {/* <td>
        <OverlayTrigger placement="top" overlay={<Tooltip>View</Tooltip>}>
          <Button
            hidden={!hasPermission(ACTION.READ, RESOURCE.PRODUCT)}
            variant="primary-light"
            className="btn btn-info-light btn-sm ms-2"
            onClick={() => navigate("details/" + productVariant.id)}
          >
            <span className="ri-eye-line fs-14"></span>
          </Button>
        </OverlayTrigger>

        <OverlayTrigger placement="top" overlay={<Tooltip>Edit</Tooltip>}>
          <Button
            hidden={!hasPermission(ACTION.UPDATE, RESOURCE.PRODUCT)}
            variant="primary-light"
            className="btn btn-warning-light btn-sm ms-2"
            onClick={() =>
              navigate("/managements-products/details/" + productVariant.id + "/edit")
            }
          >
            <span className="ri-edit-line fs-14"></span>
          </Button>
        </OverlayTrigger>

        <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
          <Button
            hidden={!hasPermission(ACTION.DELETE, RESOURCE.PRODUCT)}
            variant="primary-light"
            className="btn btn-danger-light btn-sm ms-2"
            onClick={() => handleDeleteClick(productVariant)}
          >
            <span className="ri-delete-bin-7-line fs-14"></span>
          </Button>
        </OverlayTrigger>
      </td> */}
    </tr>
  );
};
