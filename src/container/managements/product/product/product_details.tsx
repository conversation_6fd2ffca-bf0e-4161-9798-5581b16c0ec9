import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Form, Row } from "react-bootstrap";
import ReactQuill from "react-quill";
import { useNavigate, useParams } from "react-router-dom";
import Swal from "sweetalert2";
import MediaDropzone from "../../../../components/dropzone/media_dropzone";
import LazyCreatableSelect from "../../../../components/lazy-select/lazy-creatable-select";
import LazySelect from "../../../../components/lazy-select/lazy-select";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import deleteSweetAlert from "../../../../components/sweet-alerts/delete_swal";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import { useLazySelectCollectionQuery } from "../../../../services/collection";
import { useUploadMultipleMutation } from "../../../../services/media";
import {
  useCreateProductMutation,
  useDeleteProductByIdMutation,
  useLazyGetProductByIdQuery,
  useSyncProductMutation,
  useUpdateProductMutation,
} from "../../../../services/product";
import { useLazySelectProductTagQuery } from "../../../../services/product/product_tag";
import { useLazySelectProductTypeQuery } from "../../../../services/product/product_type";
import { useLazySelectProductVendorQuery } from "../../../../services/product/product_vendor";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import { getAllErrorMessages } from "../../../../utils/errors";
import { ManagementProductOptionsList } from "./product_options";
import { ManagementProductVariantList } from "./product_variant_list";

interface ManagementProductDetailsProps { }

const ManagementProductDetails: FC<ManagementProductDetailsProps> = () => {
  const { id } = useParams();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  const [getProduct] = useLazyGetProductByIdQuery();
  const [createProduct] = useCreateProductMutation();
  const [updateProduct] = useUpdateProductMutation();
  const [syncProduct] = useSyncProductMutation();
  const [deleteProduct] = useDeleteProductByIdMutation();

  const [selectProductVendor] = useLazySelectProductVendorQuery()
  const [selectProductType] = useLazySelectProductTypeQuery()
  const [selectProductTag] = useLazySelectProductTagQuery()

  const [selectCollection] = useLazySelectCollectionQuery()
  const [collections, setCollections] = useState<TCollection[]>([])

  const [upload] = useUploadMultipleMutation();
  const [mediasUploaded, setMediasUploaded] = useState<TMedia[] | { url: string }[]>([]);
  const [mediasUploading, setMediasUploading] = useState<any>([]);

  const [detailsFormData, setDetailsFormData] = useState<Partial<TProduct>>({});
  // const [variants, setVariants] = useState<Partial<TProductVariant>[]>([]);
  const [err, setErr] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();
  const returnToListPage = () => {
    navigate("/managements-products/");
  };

  useEffect(() => {
    setIsAdd(false);
    setIsEdit(false);

    if (id == 'new') {
      setIsAdd(true);
    } else {
      setIsEdit(true);
    }
  }, [id]);

  const prepareReceivedData = (data) => {
    const details: any = {
      ...data
    };

    if (data.images?.length > 0) {
      setMediasUploaded(data.images.map((image) => {
        return { url: image.src }
      }))
    }

    if (data.collections?.length > 0) {
      setCollections(data.collections)
    }

    setDetailsFormData(details);
  }

  useEffect(() => {
    switch (true) {
      case isAdd: { break; }
      case isEdit: {
        setIsLoading(true);
        getProduct(id || "")
          .unwrap()
          .then((res) => {
            prepareReceivedData(res)
          })
          .catch((error) => {
            setErr(getAllErrorMessages(error));
          })
          .finally(() => {
            setIsLoading(false);
          });
        break;
      }
      default:
        break;
    }
  }, [isAdd, isEdit]);

  const handleDetailsFormChange = (event: any) => {
    if (event) { event.preventDefault(); }

    const fieldName = event.target.getAttribute("name");
    const fieldValue = event.target.value;

    const newFormData: any = { ...detailsFormData };
    newFormData[fieldName] = fieldValue;

    setDetailsFormData(newFormData);

  };

  const handleFilesMediaChange = async (fileList: File[]) => {
    const validFiles = fileList.filter((file) => file);
    if (validFiles.length > 0) {
      upload({ file: validFiles })
        .unwrap()
        .then((uploadedFiles) => {
          setMediasUploaded((prev) => [...prev, ...uploadedFiles]);
          setMediasUploading([]);
        })
        .catch((error) => {
          const formattedErrors = error?.data?.errors?.reduce(
            (acc: any, curr: any) => {
              acc[curr.field] = curr.message;
              return acc;
            },
            {}
          );
          setErr(formattedErrors);
        });
    }
  };

  const removeFile = (index: number) => {
    if (index < mediasUploading.length) {
      setMediasUploading((prev) => prev.filter((_, i) => i !== index));
    } else {
      const uploadedIndex = index - mediasUploading.length;
      setMediasUploaded((prev) => prev.filter((_, i) => i !== uploadedIndex));
    }
  };

  useEffect(() => {
    if (mediasUploading.length > 0) {
      handleFilesMediaChange(mediasUploading);
    }
  }, [mediasUploading]);

  const prepareSubmitForm = () => {
    const preparedForm: any = {
      title: detailsFormData.title,
      description: detailsFormData.description,
      handle: detailsFormData.handle,
      shopifyProductId: detailsFormData.shopifyProductId,
      status: detailsFormData.status || 'draft',
    }

    preparedForm.productTypeName = typeof detailsFormData.productType == 'string'
      ? detailsFormData.productType : detailsFormData.productType?.name

    preparedForm.vendorName = typeof detailsFormData.vendor == 'string'
      ? detailsFormData.vendor : detailsFormData.vendor?.name

    preparedForm.tagNames = detailsFormData.tags?.map((tag) =>
      typeof tag == 'string' ? tag : tag.name
    )

    if (mediasUploaded) {
      preparedForm.mediaIds = mediasUploaded.map((media) => media.id)
    }

    if (collections) {
      preparedForm.collectionIds = collections.map((col) => col.id)
    }

    return preparedForm;
  };

  const handleAddFormSubmit = (event: any) => {
    if (event) { event.preventDefault(); }

    const newProduct = prepareSubmitForm()

    setIsLoading(true);
    createProduct({ ...newProduct })
      .unwrap()
      .then(() => {
        setDetailsFormData({});
        returnToListPage();
      })
      .catch((error) => {
        console.log(error);

        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleUpdateFormSubmit = (event: any) => {
    if (event) { event.preventDefault(); }

    const updatedProduct = prepareSubmitForm()

    setIsLoading(true);
    updateProduct({ id: id || "", ...updatedProduct })
      .unwrap()
      .then(() => {
        setDetailsFormData({});
        returnToListPage();
      })
      .catch((error) => {
        console.log(error);

        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleDeleteClick = () => {
    deleteSweetAlert({
      id: id || "",
      deleteAction: deleteProduct,
      prepareAction: (() => setIsLoading(true)),
      finishAction: (() => returnToListPage()),
      finalAction: (() => setIsLoading(false)),
    })
  };

  const handleSyncClick = (event) => {
    if (event) { event.preventDefault() }

    setErr({})
    setIsLoading(true)
    syncProduct({ id: id })
      .unwrap()
      .then(() => {
        Swal.fire({
          position: "top-end",
          icon: "success",
          title: "Product Synced!",
          showConfirmButton: false,
          timer: 1000,
        });
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error))
      })
      .finally(() => { setIsLoading(false) })
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Product Details"
                route="/managements-products/"
              />
              <Card.Subtitle>
                <Button
                  variant="info-light"
                  onClick={handleSyncClick}
                >
                  Sync
                  {
                    isLoading
                      ? <i className="spinner-border spinner-border-sm align-middle ms-2 me-0" />
                      : <i className="bi bi-arrow-repeat ms-2" />
                  }
                </Button>
              </Card.Subtitle>
            </Card.Header>
            <div>
              {err?.messages?.map((message: string, index: number) => (
                <Alert key={index} variant="danger">
                  {message}
                </Alert>
              ))}
            </div>
            <Card.Body>
              <Row>
                <Col xl={6}>
                  <Col xl={12} className="mb-3">
                    <MediaDropzone
                      uploadedFiles={mediasUploaded}
                      uploadFunction={setMediasUploading}
                      removeFile={removeFile}
                    />
                  </Col>
                  <Col className="mb-3">
                    <Form.Label>Title*</Form.Label>
                    <Form.Control
                      type="text"
                      required
                      name="title"
                      placeholder="Title"
                      value={detailsFormData?.title || ""}
                      onChange={handleDetailsFormChange}
                      isInvalid={err?.validationErrors?.title}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.title}
                    </Form.Control.Feedback>
                  </Col>
                  <Col className="mb-3">
                    <Form.Label>Description</Form.Label>
                    <ReactQuill
                      theme="snow"
                      value={detailsFormData?.description || ""}
                      onChange={(value) => setDetailsFormData((prev) => ({
                        ...prev,
                        description: value
                      }))}
                    />
                  </Col>
                </Col>
                <Col xl={6}>
                  <Col className="mb-3">
                    <Form.Label>Status</Form.Label>
                    <Form.Select
                      name="status"
                      value={detailsFormData?.status || ""}
                      onChange={handleDetailsFormChange}
                    >
                      <option value='draft'>Draft</option>
                      <option value='active'>Active</option>
                      <option value='archive'>Archived</option>
                    </Form.Select>
                  </Col>
                  <Col className="mb-3">
                    <Form.Label>Collections</Form.Label>
                    <LazySelect
                      isMulti
                      isClearable
                      selectionFunction={selectCollection}
                      label={(value) => value.title}
                      initialSelectedOptions={collections}
                      getSelectedOptions={setCollections}
                    />
                    <div
                      hidden={!err?.validationErrors?.collectionIds}
                      className="text-danger small"
                      role="alert">
                      {err?.validationErrors?.collectionIds}
                    </div>
                  </Col>
                  <Col className="mb-3">
                    <Form.Label>Product Type</Form.Label>
                    <LazyCreatableSelect
                      selectionFunction={selectProductType}
                      label={(value) => value.name}
                      initialSelectedOptions={detailsFormData?.productType}
                      getSelectedOptions={(value) =>
                        setDetailsFormData((prev) => ({ ...prev, productType: value }))
                      }
                    />
                    <div
                      hidden={!err?.validationErrors?.productTypeId}
                      className="text-danger small"
                      role="alert">
                      {err?.validationErrors?.productTypeId}
                    </div>
                  </Col>
                  <Col className="mb-3">
                    <Form.Label>Vendor</Form.Label>
                    <LazyCreatableSelect
                      selectionFunction={selectProductVendor}
                      label={(value) => value.name}
                      initialSelectedOptions={detailsFormData?.vendor}
                      getSelectedOptions={(value) =>
                        setDetailsFormData((prev) => ({ ...prev, vendor: value }))
                      }
                    />
                    <div
                      hidden={!err?.validationErrors?.vendorId}
                      className="text-danger small"
                      role="alert">
                      {err?.validationErrors?.vendorId}
                    </div>
                  </Col>
                  <Col className="mb-3">
                    <Form.Label>Tags</Form.Label>
                    <LazyCreatableSelect
                      isMulti
                      selectionFunction={selectProductTag}
                      label={(value) => value.name}
                      initialSelectedOptions={detailsFormData?.tags}
                      getSelectedOptions={(value) =>
                        setDetailsFormData((prev) => {
                          return { ...prev, tags: value }
                        })
                      }
                    />
                  </Col>
                  <Col className="mb-3">
                    <Form.Label>Handle</Form.Label>
                    <Form.Control
                      disabled={true}
                      type="text"
                      name="handle"
                      placeholder="Handle"
                      value={detailsFormData?.handle || ""}
                      onChange={handleDetailsFormChange}
                      isInvalid={err?.validationErrors?.handle}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.handle}
                    </Form.Control.Feedback>
                  </Col>
                  <Col className="mb-3">
                    <Form.Label>Shopify Product ID</Form.Label>
                    <Form.Control
                      disabled={true}
                      type="text"
                      name="shopifyProductId"
                      placeholder="Shopify Product ID"
                      value={detailsFormData?.shopifyProductId || ""}
                      onChange={handleDetailsFormChange}
                      isInvalid={err?.validationErrors?.shopifyProductId}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.shopifyProductId}
                    </Form.Control.Feedback>
                  </Col>
                  <Col className="mb-3">
                    <Form.Label>Channels</Form.Label>
                    {
                      detailsFormData?.channels?.map((channel, index) => (
                        <Fragment key={index}>
                          <div>
                            <span className={channel.active ? 'text-success' : ''}>
                              <i className="ri-store-2-line mx-1" />
                            </span>
                            {
                              !!channel.isMobile &&
                              <span className="text-info">
                                <i className="ri-smartphone-line mx-1" />
                              </span>
                            }
                            <span>
                              {channel.name}
                            </span>
                          </div>
                        </Fragment>
                      ))
                    }
                  </Col>
                </Col>
              </Row>
            </Card.Body>
            <Card.Footer>
              <div className="d-sm-flex justify-content-end">
                {isAdd ? (
                  <Button
                    hidden={!hasPermission(ACTION.CREATE, RESOURCE.PRODUCT)}
                    variant="primary-light"
                    onClick={handleAddFormSubmit}
                  >
                    Add<i className="bi bi-plus-lg ms-2"></i>
                  </Button>
                ) : (
                  <div>
                    <Button
                      hidden={!hasPermission(ACTION.DELETE, RESOURCE.PRODUCT)}
                      className="mx-2"
                      variant="danger-light"
                      onClick={handleDeleteClick}
                    >
                      Delete<i className="bi bi-trash ms-2"></i>
                    </Button>

                    <Button
                      hidden={!hasPermission(ACTION.UPDATE, RESOURCE.PRODUCT)}
                      variant="success-light"
                      onClick={handleUpdateFormSubmit}
                    >
                      Save<i className="bi bi-download ms-2"></i>
                    </Button>
                  </div>
                )}
              </div>
            </Card.Footer>
          </Card>
          <ManagementProductOptionsList
            productOptions={detailsFormData?.options}
          />
          {!isAdd &&
            <ManagementProductVariantList
              productId={(id && id != 'new') ? id : ""}
            />
          }
        </Col>
      </Row>
    </Fragment>
  );
};

export default ManagementProductDetails;
