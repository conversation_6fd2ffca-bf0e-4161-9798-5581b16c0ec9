import Swal from "sweetalert2";
import { getAllErrorMessages } from "../../utils/errors";

interface deleteSweetAlert {
  id?: string;
  deleteAction: (value?: any) => any;

  confirmText?: string;
  finishText?: string;

  prepareAction?: () => void;
  finishAction?: () => void;
  errorAction?: (error) => void;
  finalAction?: () => void;
}

const deleteSweetAlert = (input: deleteSweetAlert): void => {
  Swal.fire({
    title: "Are you sure?",
    text: input.confirmText
      ? input.confirmText
      : "You won't be able to revert this!",
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    confirmButtonText: "Yes, delete it!",
  })
    .then((result) => {
      if (result.isConfirmed) {
        input.prepareAction?.();
        // Get the result from the delete action
        let deleteResult
        if (input.id) {
          deleteResult = input.deleteAction(input.id);
        } else {
          deleteResult = input.deleteAction()
        }

        // Handle both mutation and query patterns
        if (deleteResult.unwrap) {
          // It's a mutation
          deleteResult
            .unwrap()
            .then(() => {
              Swal.fire(
                "Deleted!",
                input.finishText
                  ? input.finishText
                  : "Your data has been deleted.",
                "success"
              );
              input.finishAction?.();
            })
            .catch((error) => {
              Swal.fire(
                "Error!",
                getAllErrorMessages(error).messages?.[0],
                "error"
              );
              input.errorAction?.(error);
            })
            .finally(() => {
              input.finalAction?.();
            });
        } else {
          // It's a query
          deleteResult
            .then(() => {
              Swal.fire(
                "Deleted!",
                input.finishText
                  ? input.finishText
                  : "Your data has been deleted.",
                "success"
              );
              input.finishAction?.();
            })
            .catch((error) => {
              Swal.fire(
                "Error!",
                getAllErrorMessages(error).messages?.[0],
                "error"
              );
              input.errorAction?.(error);
            })
            .finally(() => {
              input.finalAction?.();
            });
        }
      }
    })
    .catch(() => {
      Swal.fire("Error!", "Something went wrong!", "error");
    });
};

export default deleteSweetAlert;
