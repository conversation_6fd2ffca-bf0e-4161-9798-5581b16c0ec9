import { FC, useEffect, useState } from "react";
import { <PERSON><PERSON>, Button, Form, Modal } from "react-bootstrap";
import { ErrorType } from "../../utils/error_type";
import { getAllErrorMessages } from "../../utils/errors";
import { useUpdateCommissionGroupMutation } from "../../services/affiliation/affiliatie-tier-commission-group";

interface EditCommissionGroupDialogProps {
  isShow: boolean;
  group: TAffiliateTierCommissionGroup;
  onHide: (group: TAffiliateTierCommissionGroup) => void;
  setIsLoading: (isLoading: boolean) => void;
}

const EditCommissionGroupDialog: FC<EditCommissionGroupDialogProps> = ({ isShow, group, onHide, setIsLoading }) => {
  const [err, setErr] = useState<ErrorType>();
  const [title, setTitle] = useState<string>('');
  const [titleFormError, setTitleFormError] = useState<string>('');

  const [updateCommissionGroup] = useUpdateCommissionGroupMutation();

  useEffect(() => {
    if (isShow) {
      setTitle(group.title);
    }
  }, [isShow]);

  const handleFormChange = (e) => {
    e.preventDefault();
    setTitle(e.target.value);
  }

  const handleHide = () => {
    onHide(group);
  }

  const handleConfirm = (e) => {
    e.preventDefault();

    if (!title || !title.trim()) {
      setTitleFormError('Please enter group title');
      return;
    }

    setIsLoading(true);
    setTitleFormError('');
    updateCommissionGroup({
      groupId: group.id,
      title
    })
      .unwrap()
      .then((res) => { onHide(res) })
      .catch((error) => { setErr(getAllErrorMessages(error)) })
      .finally(() => { setIsLoading(false) });
  }

  return (
    <Modal show={isShow} onHide={handleHide}>
      <Form onSubmit={handleConfirm}>
        <Modal.Header closeButton>
          <Modal.Title>Edit Group</Modal.Title>
        </Modal.Header>

        <Modal.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Form.Group className="mb-4">
            <Form.Label>Title</Form.Label>
            <Form.Control
              type="text"
              name="title"
              value={title}
              onChange={handleFormChange}
              isInvalid={titleFormError.length > 0}
            />
            <Form.Control.Feedback type="invalid">
              {titleFormError}
            </Form.Control.Feedback>
          </Form.Group>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="primary" type='submit'>OK</Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
}

export default EditCommissionGroupDialog;