import { FC, useState } from "react";
import { Button, Form, Modal } from "react-bootstrap";

interface NewCommissionGroupDialogProps {
  isShow: boolean;
  onHide: () => void;
  handleAddCommissionGroup: (title: string) => void;
}

const NewCommissionGroupDialog: FC<NewCommissionGroupDialogProps> = ({ isShow, onHide, handleAddCommissionGroup }) => {
  const [title, setTitle] = useState<string>('');
  const [titleFormError, setTitleFormError] = useState<string>('');

  const handleFormChange = (e) => {
    e.preventDefault();
    setTitle(e.target.value);
  }

  const handleConfirm = (e) => {
    e.preventDefault();

    if (!title || !title.trim()) {
      setTitleFormError('Please enter group title');
      return;
    }

    handleAddCommissionGroup(title.trim());
    onHide();
  }

  return (
    <Modal show={isShow} onHide={onHide}>
      <Form onSubmit={handleConfirm}>
        <Modal.Header closeButton>
          <Modal.Title>New Group</Modal.Title>
        </Modal.Header>

        <Modal.Body>
          <Form.Group className="mb-4">
            <Form.Label>Title</Form.Label>
            <Form.Control
              type="text"
              name="title"
              value={title}
              onChange={handleFormChange}
              isInvalid={titleFormError.length > 0}
            />
            <Form.Control.Feedback type="invalid">
              {titleFormError}
            </Form.Control.Feedback>
          </Form.Group>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="primary" type='submit'>OK</Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
}

export default NewCommissionGroupDialog;